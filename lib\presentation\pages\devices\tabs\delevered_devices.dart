import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class DeleveredDevices extends StatelessWidget {
  const DeleveredDevices({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return Column(
      spacing: 18.0,
      children: [
        CardHighlight(
          child: Row(
            spacing: 18,
            children: [
              Expanded(
                flex: 1,
                child: TextBox(
                  expands: false,
                  placeholder: lang.search,
                  prefix: Padding(
                    padding: EdgeInsets.all(8),
                    child: Icon(CarbonIcons.search),
                  ),
                ),
              ),
              Spacer(flex: 3),
              IconButton(
                icon: Icon(FluentIcons.refresh, size: 18.0),
                onPressed: () {},
              ),
            ],
          ),
        ),
        CommandBar(
          overflowBehavior: CommandBarOverflowBehavior.wrap,
          primaryItems: [
            CommandBarButton(
              icon: const Icon(CarbonIcons.queued, size: 22),
              label: Text(lang.phase),
              tooltip: lang.phase_tooltip,
              onPressed: () {
                // Create something new!
              },
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.edit),
              label: Text(lang.edit),
              tooltip: lang.edit_device_tooltip,
              onPressed: () {
                // Create something new!
              },
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.delete),
              label: Text(lang.delete),
              tooltip: lang.remove_device_tooltip,
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(CarbonIcons.delivery, size: 22),
              label: Text(lang.cancel_delivery),
              tooltip: lang.cancel_delivery_tooltip,
              onPressed: () {},
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.full_history),
              label: Text(lang.traceability),
              tooltip: lang.traceability_tooltip,
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(FluentIcons.user_event),
              label: Text(lang.technician),
              tooltip: lang.technician_tooltip,
              onPressed: () {},
            ),
          ],
        ),
        // Expanded(child: DevicesDataGrid()),
      ],
    );
  }
}
