class StockInvoicesProductsModel {
  StockInvoicesProductsModel({
    required this.id,
    required this.family,
    required this.reference,
    required this.partNumber,
    required this.quantity,
    required this.unitPrice,
    required this.priceType,
    required this.total,
    required this.date,
  });
  final int id;
  final String family;
  final String reference;
  final String partNumber;
  final int quantity;
  final double unitPrice;
  final int priceType;
  final double total;
  final DateTime date;
}
