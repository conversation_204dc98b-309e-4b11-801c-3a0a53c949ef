import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/devices_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class VatDevicesDatagrid extends StatefulWidget {
  const VatDevicesDatagrid({super.key});

  @override
  State<VatDevicesDatagrid> createState() => _VatDevicesDatagridState();
}

class _VatDevicesDatagridState extends State<VatDevicesDatagrid> {
  List<DevicesTableModel> employees = <DevicesTableModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(
        headerColor: FluentThemeData().accentColor.lightest,
      ),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'type',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TYPE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'brand',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.brand.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'serei',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.serie.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'model',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.model.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'serial_number',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.serial_n.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'issue',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.issue.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'price',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("PRICE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'services',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("SERVICES", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<DevicesTableModel> getEmployeeData() {
    return [];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<DevicesTableModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<String>(columnName: 'type', value: e.type),
                  DataGridCell<String>(columnName: 'brand', value: e.brand),
                  DataGridCell<String>(columnName: 'serei', value: e.serei),
                  DataGridCell<String>(columnName: 'model', value: e.model),
                  DataGridCell<String>(
                    columnName: 'serial_number',
                    value: e.serialNumber,
                  ),
                  DataGridCell<String>(columnName: 'issue', value: e.issue),
                  // DataGridCell<double>(columnName: 'price', value: e.price),
                  // DataGridCell<String>(
                  //   columnName: 'services',
                  //   value: e.services,
                  // ),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
