import 'package:fluent_ui/fluent_ui.dart';

class <PERSON><PERSON><PERSON>light extends StatefulWidget {
  const CardHighlight({super.key, this.backgroundColor, required this.child});

  final Widget child;

  final Color? backgroundColor;

  @override
  State<CardHighlight> createState() => _CardHighlightState();
}

class _CardHighlightState extends State<CardHighlight>
    with AutomaticKeepAliveClientMixin<CardHighlight> {
  final GlobalKey expanderKey = GlobalKey<ExpanderState>();

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = FluentTheme.of(context);

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(color: theme.resources.controlStrokeColorSecondary),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: <PERSON><PERSON>(
          backgroundColor: widget.backgroundColor,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: <PERSON><PERSON>(
              alignment: AlignmentDirectional.topStart,
              child: <PERSON><PERSON><PERSON><PERSON>(width: double.infinity, child: widget.child),
            ),
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
