import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datasources/situation_data_source.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/providers/clients/transactions/client_transactions_notifier.dart';

class SituationDataGrid extends ConsumerWidget {
  const SituationDataGrid({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    final client = ref.watch(selectedClientProvider);
    if (client == null) {
      return const Center(child: Text('No client selected'));
    }
    final state = ref.watch(clientTransactionsProvider(client));
    final dataSource = SituationDataSource(
      context,
      transactions: state.transactions,
    );
    return SfDataGridTheme(
      data: SfDataGridThemeData(
        headerColor: Colors.blue.light,
        selectionColor: Colors.blue.lightest,
        gridLineColor: Colors.transparent,
      ),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            columnWidthMode: ColumnWidthMode.fill,
            selectionMode: SelectionMode.single,
            source: dataSource,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'ID',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('N°', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'CurrentBalance',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Current Balance', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Payment',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Payment Amount', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Type',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Type', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'OldBalance',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Old Balance', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'NewBalance',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('New Balance', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Date',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Date', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Time',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Time', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Warranty',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('End of Warranty', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Note',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Note', style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
