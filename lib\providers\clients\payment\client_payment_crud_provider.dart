import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';
import 'package:workshop_studio/services/database_service.dart';

final clientPaymentCrudProvider = Provider<ClientPaymentCRUD>((ref) {
  final dbService = ref.read(mysqlServiceProvider);
  return ClientPaymentCRUD(dbService);
});

class ClientPaymentCRUD {
  final MySQLService _dbService;

  ClientPaymentCRUD(this._dbService);

  Future<bool> updateClientDebt(int clientId, double debts) async {
    final result = await _dbService.query(
      'UPDATE clients SET debts = :debts WHERE id = :client_id',
      params: {'debts': debts, 'client_id': clientId},
    );
    return result.affectedRows.toInt() > 0;
  }

  Future<int> createPayment({
    required int clientId,
    required double amount,
    required int paymentMethod,
    required String note,
  }) async {
    final result = await _dbService.query(
      '''
      INSERT INTO client_sold (
        c_id, payment_type, current_balance, payment, old_balance, new_balance, date, time, note
      ) VALUES (
        :c_id, :payment_type, :current_balance, :payment, :old_balance, :new_balance, CURDATE(), CURTIME(), :note
      )
    ''',
      params: {
        'c_id': clientId,
        'payment_type': paymentMethod,
        'current_balance': amount,
        'payment': amount,
        'old_balance': amount,
        'new_balance': amount,
        'note': note,
      },
    );
    return result.lastInsertID.toInt();
  }
}
