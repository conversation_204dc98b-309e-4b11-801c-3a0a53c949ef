import 'package:flutter/widgets.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/cash_actual/widgets/debts_datagrid.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/cash_actual/widgets/register_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class CashActualPage extends StatelessWidget {
  const CashActualPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 18.0,
      children: [
        Flexible(
          flex: 1,
          child: Column(
            spacing: 18.0,
            children: [
              CardHighlight(
                child: Text(
                  "Total cash register: 75600.00 DZA",
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              Expanded(child: CardHighlight(child: RegisterDataGrid())),
            ],
          ),
        ),
        Flexible(
          flex: 1,
          child: Column(
            spacing: 18.0,
            children: [
              CardHighlight(
                child: Text(
                  "Total debts: 4600.00 DZA",
                  style: TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              Expanded(child: CardHighlight(child: DebtsDataGrid())),
            ],
          ),
        ),
      ],
    );
  }
}
