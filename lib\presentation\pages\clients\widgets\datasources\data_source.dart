import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/constants/constants.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';

class ClientDataSource extends DataGridSource {
  final BuildContext context;
  final ClientModel? selectedClient;
  ClientDataSource(
    this.context, {
    required List<ClientModel> clients,
    this.selectedClient,
  }) {
    _dataGridRows =
        clients.map<DataGridRow>((client) {
          return DataGridRow(
            cells: [
              DataGridCell<int>(columnName: 'ID', value: client.id),
              DataGridCell<String>(columnName: 'Name', value: client.cName),
              DataGridCell<String>(columnName: 'Phone 1', value: client.cPhone),
              DataGridCell<String>(
                columnName: 'Phone 2',
                value: client.cPhone2,
              ),
              DataGridCell<int>(columnName: 'Type', value: client.cType),
              DataGridCell<String>(columnName: 'Date', value: client.date),
              DataGridCell<String>(columnName: 'Time', value: client.time),
              DataGridCell<double>(columnName: 'Debts', value: client.debts),
              DataGridCell<String>(columnName: 'User', value: client.user),
            ],
          );
        }).toList();
    notifyListeners();
  }

  List<DataGridRow> _dataGridRows = [];

  @override
  List<DataGridRow> get rows => _dataGridRows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int id =
          row.getCells().firstWhere((cell) => cell.columnName == 'ID').value;
      if (selectedClient != null && selectedClient!.id == id) {
        return Colors.blue.lightest; // Highlight selected row
      }
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50); // Alternate row color
      }
      return Colors.transparent; // Default row color
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map((cell) {
            return Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(8.0),
              child: Text(
                textAlign: TextAlign.center,
                cell.columnName == 'Type'
                    ? getClientType(index: cell.value, context: context)
                    : cell.value.toString().toUpperCase(),
              ),
            );
          }).toList(),
    );
  }
}
