import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/providers/clients/timeline_filter_provider.dart';

// class DevicesAtWorkshopTimeline extends ConsumerWidget {

class DevicesAtWorkshopTimeline extends ConsumerWidget {
  const DevicesAtWorkshopTimeline({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lang = AppLocalizations.of(context);
    final filter = ref.watch(timelineFilterProviderFamily('devicesAtWorkshop'));
    final notifier = ref.read(
      timelineFilterProviderFamily('devicesAtWorkshop').notifier,
    );
    final TextStyle textStyle = const TextStyle(fontSize: 13);
    final TextStyle listTileStyle = TextStyle(fontSize: 13);
    final Widget spaceSize = SizedBox(height: 18);

    return CardHighlight(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: SingleChildScrollView(
          child: Column(
            // spacing: 18,
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                runAlignment: WrapAlignment.center,
                alignment: WrapAlignment.center,
                runSpacing: 8,
                spacing: 8,
                children: [
                  Icon(FluentIcons.date_time2, size: 22, color: Colors.blue),
                  Padding(
                    padding: const EdgeInsets.only(top: 3.0),
                    child: Text(lang.timeline),
                  ),
                ],
              ),
              spaceSize,
              const Divider(),
              spaceSize,
              RadioButton(
                checked: filter.type == TimelineFilterType.all,
                onChanged: (_) => notifier.setAll(),
                content: Text(lang.all, style: textStyle),
              ),
              spaceSize,
              RadioButton(
                checked: filter.type == TimelineFilterType.currentYear,
                onChanged: (_) => notifier.setCurrentYear(),
                content: Text(lang.current_year, style: textStyle),
              ),
              spaceSize,
              RadioButton(
                checked: filter.type == TimelineFilterType.currentMonth,
                onChanged: (_) => notifier.setCurrentMonth(),
                content: Text(lang.current_month, style: textStyle),
              ),
              spaceSize,
              RadioButton(
                checked: filter.type == TimelineFilterType.currentWeek,
                onChanged: (_) => notifier.setCurrentWeek(),
                content: Text(lang.current_week, style: textStyle),
              ),
              spaceSize,
              const Divider(),
              spaceSize,
              RadioButton(
                checked: filter.type == TimelineFilterType.customRange,
                onChanged: (_) {
                  final now = DateTime.now();
                  final startOfYear = DateTime(now.year, 1, 1);
                  final today = DateTime(now.year, now.month, now.day);
                  notifier.setCustomRange(startOfYear, today);
                },
                content: Text(lang.timeline_picker, style: textStyle),
              ),
              spaceSize,
              DatePicker(
                header: lang.from,
                selected: filter.from,
                fieldFlex: const [2, 3, 2],
                onChanged: (date) => notifier.setFrom(date),
              ),
              spaceSize,
              DatePicker(
                header: lang.to,
                selected: filter.to,
                fieldFlex: const [2, 3, 2],
                onChanged: (date) => notifier.setTo(date),
              ),
              spaceSize,
              Divider(),
              spaceSize,
              Wrap(
                children: [
                  Container(
                    width: 15,
                    height: 15,
                    decoration: BoxDecoration(
                      color: Color(0xeee67e22),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  SizedBox(width: 10),
                  Text(lang.confirmation, style: listTileStyle),
                ],
              ),
              spaceSize,
              Wrap(
                children: [
                  Container(
                    width: 15,
                    height: 15,
                    decoration: BoxDecoration(
                      color: Color(0xee2980b9),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  SizedBox(width: 10),
                  Text(lang.confirmed, style: listTileStyle),
                ],
              ),
              spaceSize,
              Wrap(
                children: [
                  Container(
                    width: 15,
                    height: 15,
                    decoration: BoxDecoration(
                      color: Color(0xeec0392b),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  SizedBox(width: 10),
                  Text(lang.rejected, style: listTileStyle),
                ],
              ),
              spaceSize,
              Wrap(
                children: [
                  Container(
                    width: 15,
                    height: 15,
                    decoration: BoxDecoration(
                      color: Color(0xee0a8d43),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  SizedBox(width: 10),
                  Text(lang.repaired, style: listTileStyle),
                ],
              ),
              spaceSize,
              Wrap(
                children: [
                  Container(
                    width: 15,
                    height: 15,
                    decoration: BoxDecoration(
                      color: Color(0xeef24407),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  SizedBox(width: 10),
                  Text(lang.not_repaired, style: listTileStyle),
                ],
              ),
              spaceSize,
              Wrap(
                children: [
                  Container(
                    width: 15,
                    height: 15,
                    decoration: BoxDecoration(
                      color: Color(0xee5f0a30),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  SizedBox(width: 10),
                  Text(lang.parts_unavailable, style: listTileStyle),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
