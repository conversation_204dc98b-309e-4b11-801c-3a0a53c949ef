import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class ReceiptsClientsDataGrid extends StatefulWidget {
  const ReceiptsClientsDataGrid({super.key});

  @override
  State<ReceiptsClientsDataGrid> createState() =>
      _ReceiptsClientsDataGridState();
}

class _ReceiptsClientsDataGridState extends State<ReceiptsClientsDataGrid> {
  List<ClientModel> employees = <ClientModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'id',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.id, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'name',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.name.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'phone_number',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.phone_n.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'phone_number_2',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.phone_n.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'type',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.type.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'date',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.date.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'time',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.time.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'debts',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.debts.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'user',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.user.toUpperCase(), style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<ClientModel> getEmployeeData() {
    return [];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<ClientModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'id', value: e.id),
                  DataGridCell<String>(columnName: 'name', value: e.cName),
                  DataGridCell<String>(
                    columnName: 'phone_number',
                    value: e.cPhone,
                  ),
                  DataGridCell<String>(
                    columnName: 'phone_number_2',
                    value: e.cPhone2,
                  ),
                  DataGridCell<int>(columnName: 'type', value: e.cType),
                  DataGridCell<String>(columnName: 'date', value: e.date),
                  DataGridCell<String>(columnName: 'time', value: e.time),
                  DataGridCell<double>(columnName: 'debts', value: e.debts),
                  DataGridCell<String>(columnName: 'user', value: e.user),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
