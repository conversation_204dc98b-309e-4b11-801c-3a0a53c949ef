import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/vat_transactions/vat_transactions_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class VatTransactionsDatagrid extends StatefulWidget {
  const VatTransactionsDatagrid({super.key});

  @override
  State<VatTransactionsDatagrid> createState() =>
      _VatTransactionsDatagridState();
}

class _VatTransactionsDatagridState extends State<VatTransactionsDatagrid> {
  List<VatTransactionsTableModel> employees = <VatTransactionsTableModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(
        headerColor: FluentThemeData().accentColor.lightest,
      ),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerRowHeight: 80,
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResizeEnd,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'n',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("N°", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'client',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.client, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'total_ht',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TOTAL H.T", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'tva',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TVA", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'total_tva',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TOTAL TVA", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'new_payable',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("NEW PAYABLE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'type',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.type, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'date',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.date, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'time',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.time, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'end_of_warranty',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("END OF WARRANTY", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'transaction_note',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TRANSACTION NOTE", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<VatTransactionsTableModel> getEmployeeData() {
    return [
      VatTransactionsTableModel(
        id: 3613,
        client: "MADI HOUCINE",
        totalHt: 15000.00,
        tva: 0.00,
        totalTva: 15000.00,
        netPayable: 15000.00,
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
        endOfWarranty: 5,
        transactionNote: "transaction not here",
      ),
      VatTransactionsTableModel(
        id: 3613,
        client: "MADI HOUCINE",
        totalHt: 15000.00,
        tva: 0.00,
        totalTva: 15000.00,
        netPayable: 15000.00,
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
        endOfWarranty: 5,
        transactionNote: "transaction not here",
      ),
      VatTransactionsTableModel(
        id: 3613,
        client: "MADI HOUCINE",
        totalHt: 15000.00,
        tva: 0.00,
        totalTva: 15000.00,
        netPayable: 15000.00,
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
        endOfWarranty: 5,
        transactionNote: "transaction not here",
      ),
      VatTransactionsTableModel(
        id: 3613,
        client: "MADI HOUCINE",
        totalHt: 15000.00,
        tva: 0.00,
        totalTva: 15000.00,
        netPayable: 15000.00,
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
        endOfWarranty: 5,
        transactionNote: "transaction not here",
      ),
      VatTransactionsTableModel(
        id: 3613,
        client: "MADI HOUCINE",
        totalHt: 15000.00,
        tva: 0.00,
        totalTva: 15000.00,
        netPayable: 15000.00,
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
        endOfWarranty: 5,
        transactionNote: "transaction not here",
      ),
      VatTransactionsTableModel(
        id: 3613,
        client: "MADI HOUCINE",
        totalHt: 15000.00,
        tva: 0.00,
        totalTva: 15000.00,
        netPayable: 15000.00,
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
        endOfWarranty: 5,
        transactionNote: "transaction not here",
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<VatTransactionsTableModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'n', value: e.id),
                  DataGridCell<String>(columnName: 'client', value: e.client),
                  DataGridCell<double>(
                    columnName: 'total_ht',
                    value: e.totalHt,
                  ),
                  DataGridCell<double>(columnName: 'tva', value: e.tva),
                  DataGridCell<double>(
                    columnName: 'total_tva',
                    value: e.totalTva,
                  ),
                  DataGridCell<double>(
                    columnName: 'net_payable',
                    value: e.netPayable,
                  ),
                  DataGridCell<int>(columnName: 'type', value: e.type),
                  DataGridCell<DateTime>(columnName: 'date', value: e.date),
                  DataGridCell<DateTime>(columnName: 'time', value: e.time),
                  DataGridCell<int>(
                    columnName: 'end_of_warranty',
                    value: e.endOfWarranty,
                  ),
                  DataGridCell<String>(
                    columnName: 'transaction_note',
                    value: e.transactionNote,
                  ),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
