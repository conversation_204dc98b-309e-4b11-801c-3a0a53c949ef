import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/presentation/pages/reception/widgets/data_inputs.dart';
import 'package:workshop_studio/presentation/pages/reception/widgets/reception_data_grid.dart';

class ReceptionPage extends StatelessWidget {
  const ReceptionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ScaffoldPage.scrollable(
      children: [DataInputs(), Sized<PERSON>ox(height: 18), ReceptionDataGrid()],
    );
  }
}
