import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/models/device/device_model.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';
import 'package:workshop_studio/providers/language_provider.dart';
import 'package:workshop_studio/services/database_service.dart';
import 'package:workshop_studio/services/frontline_sms_service.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';

final deviceCrudProvider = Provider.family<DeviceCRUD, String>((ref, uid) {
  final dbService = ref.read(mysqlServiceProvider);
  return DeviceCRUD(dbService, ref);
});

class DeviceCRUD {
  final MySQLService _dbService;
  final Ref _ref;
  DeviceCRUD(this._dbService, this._ref);

  /// Get localized messages based on current locale
  AppLocalizations _getLocalizations() {
    final currentLocale = _ref.read(appLanguageProvider);
    return lookupAppLocalizations(currentLocale);
  }

  Future<int> createDevice(DeviceModel device) async {
    final result = await _dbService.query(
      '''
      INSERT INTO client_machines (
        c_id, b_id, m_type, m_brand, m_serie, m_model, m_serial_num, m_problem,
        m_estimated_price, price, warranty, emergency, technician, deadline, note,
        date, time, phase, dateupdate, delivery, datedelivery, calculated
      ) VALUES (
        :c_id, :b_id, :m_type, :m_brand, :m_serie, :m_model, :m_serial_num, :m_problem,
        :m_estimated_price, :price, :warranty, :emergency, :technician, :deadline, :note,
        :date, :time, :phase, :dateupdate, :delivery, :datedelivery, :calculated
      )
      ''',
      params: {
        'c_id': device.clientId,
        'b_id': device.billId,
        'm_type': device.machineType,
        'm_brand': device.machineBrand,
        'm_serie': device.machineSerie,
        'm_model': device.machineModel,
        'm_serial_num': device.serialNumber,
        'm_problem': device.problem,
        'm_estimated_price': device.estimatedPrice,
        'price': device.price,
        'warranty': device.warranty,
        'emergency': device.emergency,
        'technician': device.technician,
        'deadline': device.deadline,
        'note': device.note,
        'date': device.date,
        'time': device.time,
        'phase': device.phase,
        'dateupdate': device.dateUpdate,
        'delivery': device.delivery,
        'datedelivery': device.dateDelivery,
        'calculated': device.calculated,
      },
    );
    return result.lastInsertID.toInt();
  }

  Future<bool> deleteDevice(int machineId) async {
    final result = await _dbService.query(
      'DELETE FROM client_machines WHERE m_id = :m_id',
      params: {'m_id': machineId},
    );
    return result.affectedRows.toInt() > 0;
  }

  Future<bool> updateTechnician({
    required int workerId,
    required int clientId,
    required int machineId,
    required int phase,
    required String user,
    required String technician,
    required DateTime date,
    required String time,
  }) async {
    final result = await _dbService.query(
      "INSERT INTO activity(w_id, c_id, m_id, date) VALUES(:w_id, :c_id, :m_id, :date)",
      params: {
        'w_id': workerId,
        'c_id': clientId,
        'm_id': machineId,
        'date': date,
      },
    );
    if (result.affectedRows.toInt() > 0) {
      await _dbService.query(
        "INSERT INTO machine_historic(m_id, phase, user, technician, date, time) VALUES(:m_id, :phase, :user, :technician, :date, :time)",
        params: {
          'm_id': machineId,
          'phase': phase,
          'user': user,
          'technician': technician,
          'date': date,
          'time': time,
        },
      );
    }
    return result.affectedRows.toInt() > 0;
  }

  Future<bool> updatePhase({
    required int machineId,
    required int phase,
    required String user,
    required dynamic date,
    required dynamic time,
  }) async {
    final result = await _dbService.query(
      '''
      UPDATE client_machines SET
        phase = :phase,
        dateupdate = CURDATE()
      WHERE m_id = :m_id
      ''',
      params: {'m_id': machineId, 'phase': phase},
    );
    if (result.affectedRows.toInt() > 0) {
      await _dbService.query(
        "INSERT INTO machine_historic(m_id, phase, user, date, time) VALUES(:m_id, :phase, :user, :date, :time)",
        params: {
          'm_id': machineId,
          'phase': phase,
          'user': user,
          'date': date,
          'time': time,
        },
      );
    }

    return result.affectedRows.toInt() > 0;
  }

  Future<bool> updatePrice(
    int machineId,
    double price,
    double estimatedPrice,
  ) async {
    final result = await _dbService.query(
      '''
      UPDATE client_machines SET
        price = :price,
        m_estimated_price = :estimated_price,
        dateupdate = CURDATE()
      WHERE m_id = :m_id
      ''',
      params: {
        'm_id': machineId,
        'price': price,
        'estimated_price': estimatedPrice,
      },
    );
    return result.affectedRows.toInt() > 0;
  }

  Future<bool> editDevice({
    required int machineId,
    required String machineType,
    required String machineBrand,
    String? machineSerie,
    String? machineModel,
    String? serialNumber,
    String? problem,
    required double estimatedPrice,
    required bool warranty,
    required bool emergency,
    required int deadline,
    String? note,
  }) async {
    final result = await _dbService.query(
      '''
      UPDATE client_machines SET
        m_type = :m_type,
        m_brand = :m_brand,
        m_serie = :m_serie,
        m_model = :m_model,
        m_serial_num = :m_serial_num,
        m_problem = :m_problem,
        m_estimated_price = :m_estimated_price,
        warranty = :warranty,
        emergency = :emergency,
        deadline = :deadline,
        note = :note,
        dateupdate = CURDATE()
      WHERE m_id = :m_id
      ''',
      params: {
        'm_id': machineId,
        'm_type': machineType,
        'm_brand': machineBrand,
        'm_serie': machineSerie,
        'm_model': machineModel,
        'm_serial_num': serialNumber,
        'm_problem': problem,
        'm_estimated_price': estimatedPrice,
        'warranty': warranty ? 1 : 0,
        'emergency': emergency ? 1 : 0,
        'deadline': deadline,
        'note': note,
      },
    );
    return result.affectedRows.toInt() > 0;
  }

  Future<String?> fetchSmsTemplate({
    required int phase,
    required String clientName,
    required String machineType,
    required String machineModel,
    required String price,
    required String receiptN,
  }) async {
    String? template;

    try {
      switch (phase) {
        case 4: // Repaired
          final result = await _dbService.query(
            'SELECT repaired FROM repaired_sms',
          );
          if (result.rows.isNotEmpty) {
            template = result.rows.first.colByName('repaired');
          }
          break;
        case 5: // Not repaired
          final result = await _dbService.query(
            'SELECT not_repaired FROM not_repaired_sms',
          );
          if (result.rows.isNotEmpty) {
            template = result.rows.first.colByName('not_repaired');
          }
          break;
        case 2: // To confirm
          final result = await _dbService.query(
            'SELECT to_confirm FROM to_confirm_sms',
          );
          if (result.rows.isNotEmpty) {
            template = result.rows.first.colByName('to_confirm');
          }
          break;
        case 6: // Missing pieces
          final result = await _dbService.query(
            'SELECT missing_pieces FROM missing_pieces_sms',
          );
          if (result.rows.isNotEmpty) {
            template = result.rows.first.colByName('missing_pieces');
          }
          break;
        case 7: // Rejected
          final result = await _dbService.query(
            'SELECT rejected FROM rejected_sms',
          );
          if (result.rows.isNotEmpty) {
            template = result.rows.first.colByName('rejected');
          }
          break;
        default:
          return null;
      }
    } catch (e) {
      // Handle any database errors silently,
      return null;
    }
    if (template?.isNotEmpty == true) {
      // Replace placeholders with actual values
      template = template!
          .replaceAll('<client name>', clientName.toUpperCase())
          .replaceAll('<machine>', machineType.toUpperCase())
          .replaceAll('<model>', machineModel.toUpperCase())
          .replaceAll('<receipt number>', receiptN.toString())
          .replaceAll('<price>', price.toString());
    }
    return template?.isNotEmpty == true ? template : null;
  }

  /// Fetch existing remarks for a machine
  Future<String?> getRemarks({required int machineId}) async {
    try {
      final result = await _dbService.query(
        'SELECT remarks FROM repaired_remarks WHERE m_id = :m_id',
        params: {'m_id': machineId},
      );

      if (result.rows.isNotEmpty) {
        return result.rows.first.colByName('remarks');
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Save remarks for a machine
  /// check if remarks exist, then update or insert
  Future<String> saveRemarks({
    required int machineId,
    required String remarks,
  }) async {
    try {
      // Check if remarks already exist for this machine in repaired_remarks table
      final existingResult = await _dbService.query(
        'SELECT * FROM repaired_remarks WHERE m_id = :m_id',
        params: {'m_id': machineId},
      );

      if (existingResult.rows.isNotEmpty) {
        // Update existing remarks
        final updateResult = await _dbService.query(
          'UPDATE repaired_remarks SET remarks = :remarks WHERE m_id = :m_id',
          params: {'m_id': machineId, 'remarks': remarks},
        );

        if (updateResult.affectedRows.toInt() > 0) {
          return 'updated'; // Equivalent to self.headers[self.lang]['upd']
        } else {
          return 'error';
        }
      } else {
        // Insert new remarks only if remarks is not empty
        if (remarks.isNotEmpty) {
          final insertResult = await _dbService.query(
            'INSERT INTO repaired_remarks(m_id, remarks) VALUES(:m_id, :remarks)',
            params: {'m_id': machineId, 'remarks': remarks},
          );

          if (insertResult.affectedRows.toInt() > 0) {
            return 'saved'; // Equivalent to self.headers[self.lang]['svd']
          } else {
            return 'error';
          }
        } else {
          return 'empty'; // Remarks is empty, nothing to save
        }
      }
    } catch (e) {
      // Handle any database errors
      return 'error';
    }
  }

  /// Send SMS via Frontline SMS
  /// Follows the pattern from Python code: get frontline config, send SMS, log to database
  Future<Map<String, dynamic>> sendSms({
    required int clientId,
    required int machineId,
    required String destination,
    required String message,
    required int phase,
    required String user,
  }) async {
    try {
      // Get frontline configuration from database
      final frontlineResult = await _dbService.query(
        'SELECT address, port FROM frontline',
      );

      if (frontlineResult.rows.isEmpty) {
        final lang = _getLocalizations();
        return {'success': false, 'message': lang.frontlineSmsNotConfigured};
      }

      final frontlineConfig = frontlineResult.rows.first;
      final address = frontlineConfig.colByName('address') ?? 'localhost';
      final port = frontlineConfig.colByName('port') ?? '8011';

      // Send SMS via Frontline SMS service with timeout handling
      final smsResult = await FrontlineSmsService.sendSms(
        address: address,
        port: port,
        destination: destination,
        message: message,
      );

      if (smsResult) {
        // SMS sent successfully, log to database
        final now = DateTime.now();
        final dateStr =
            '${now.year}/${now.month.toString().padLeft(2, '0')}/${now.day.toString().padLeft(2, '0')}';
        final timeStr =
            '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';

        // Insert into sms_status table
        await _dbService.query(
          'INSERT INTO sms_status(c_id, m_id, sms_type, date, time) VALUES(:c_id, :m_id, :sms_type, :date, :time)',
          params: {
            'c_id': clientId,
            'm_id': machineId,
            'sms_type': phase,
            'date': dateStr,
            'time': timeStr,
          },
        );

        // Insert into machine_historic table
        await _dbService.query(
          'INSERT INTO machine_historic(m_id, phase, sms_type, user, date, time) VALUES(:m_id, :phase, :sms_type, :user, :date, :time)',
          params: {
            'm_id': machineId,
            'phase': phase,
            'sms_type': phase,
            'user': user,
            'date': dateStr,
            'time': timeStr,
          },
        );

        final lang = _getLocalizations();
        return {'success': true, 'message': lang.smsSentSuccessfully};
      } else {
        final lang = _getLocalizations();
        return {'success': false, 'message': lang.failedToSendSmsViaFrontline};
      }
    } on TimeoutException catch (e) {
      final lang = _getLocalizations();
      return {
        'success': false,
        'message': lang.smsServiceTimeout(e.message ?? lang.connectionTimedOut),
      };
    } catch (e) {
      final lang = _getLocalizations();
      return {'success': false, 'message': lang.failedToSendSms(e.toString())};
    }
  }
}
