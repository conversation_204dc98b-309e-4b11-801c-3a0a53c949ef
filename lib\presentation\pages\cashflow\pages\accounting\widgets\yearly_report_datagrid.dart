import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
// import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/acounting/yearly_report_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class YearlyReportDataGrid extends StatefulWidget {
  const YearlyReportDataGrid({super.key});

  @override
  State<YearlyReportDataGrid> createState() => _YearlyReportDataGridState();
}

class _YearlyReportDataGridState extends State<YearlyReportDataGrid> {
  List<YearlyReportModel> employees = <YearlyReportModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    // final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'year',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("YEAR", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'cash',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("CASH", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'estimated_price',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("ESTIMATED PRICE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'total',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TOTAL", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<YearlyReportModel> getEmployeeData() {
    return [
      YearlyReportModel(
        year: DateTime.now(),
        cash: 30000.00,
        estimatedPrice: 4500.00,
        total: 6000.00,
      ),
      YearlyReportModel(
        year: DateTime.now(),
        cash: 30000.00,
        estimatedPrice: 4500.00,
        total: 6000.00,
      ),
      YearlyReportModel(
        year: DateTime.now(),
        cash: 30000.00,
        estimatedPrice: 4500.00,
        total: 6000.00,
      ),
      YearlyReportModel(
        year: DateTime.now(),
        cash: 30000.00,
        estimatedPrice: 4500.00,
        total: 6000.00,
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<YearlyReportModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<DateTime>(columnName: 'date', value: e.year),
                  DataGridCell<double>(columnName: 'cash', value: e.cash),
                  DataGridCell<double>(
                    columnName: 'estimated_price',
                    value: e.estimatedPrice,
                  ),
                  DataGridCell<double>(columnName: 'total', value: e.total),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
