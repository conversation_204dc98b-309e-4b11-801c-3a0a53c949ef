import 'dart:convert';

class ClientSoldTvaModel {
  final int id;
  final int cId;
  final dynamic machinesIds;
  final int paymentType;
  final int tvav;
  final double ttc;
  final double ttva;
  final double tht;
  final String? note;
  final String date;
  final String time;
  final String? dateWarranty;

  ClientSoldTvaModel({
    required this.id,
    required this.cId,
    required this.machinesIds,
    required this.paymentType,
    required this.tvav,
    required this.ttc,
    required this.ttva,
    required this.tht,
    this.note,
    required this.date,
    required this.time,
    this.dateWarranty,
  });

  factory ClientSoldTvaModel.fromJson(Map<String, dynamic> json) {
    return ClientSoldTvaModel(
      id: json['id'],
      cId: json['c_id'],
      machinesIds: json['machines_ids'],
      paymentType: json['payment_type'],
      tvav: json['tvav'],
      ttc: json['ttc'].toDouble(),
      ttva: json['ttva'].toDouble(),
      tht: json['tht'].toDouble(),
      note: json['note'],
      date: json['date'],
      time: json['time'],
      dateWarranty: json['date_warranty'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'c_id': cId,
      'machines_ids': jsonEncode(machinesIds),
      'payment_type': paymentType,
      'tvav': tvav,
      'ttc': ttc,
      'ttva': ttva,
      'tht': tht,
      'note': note,
      'date': date,
      'time': time,
      'date_warranty': dateWarranty ?? '',
    };
  }

  ClientSoldTvaModel copyWith({
    int? id,
    int? cId,
    Map<String, dynamic>? machinesIds,
    int? paymentType,
    int? tvav,
    double? ttc,
    double? ttva,
    double? tht,
    String? note,
    String? date,
    String? time,
    String? dateWarranty,
  }) {
    return ClientSoldTvaModel(
      id: id ?? this.id,
      cId: cId ?? this.cId,
      machinesIds: machinesIds ?? this.machinesIds,
      paymentType: paymentType ?? this.paymentType,
      tvav: tvav ?? this.tvav,
      ttc: ttc ?? this.ttc,
      ttva: ttva ?? this.ttva,
      tht: tht ?? this.tht,
      note: note ?? this.note,
      date: date ?? this.date,
      time: time ?? this.time,
      dateWarranty: dateWarranty ?? this.dateWarranty,
    );
  }
}
