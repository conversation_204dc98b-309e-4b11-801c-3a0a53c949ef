import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/stock/pages/products/widgets/products_details_datagrid.dart';
import 'package:workshop_studio/presentation/pages/stock/pages/products/widgets/products_table_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class ProductsStockPage extends StatelessWidget {
  const ProductsStockPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 10,
      children: [
        CardHighlight(
          child: Row(
            spacing: 18,
            children: [
              Expanded(
                flex: 2,
                child: TextBox(
                  expands: false,
                  placeholder: lang.search,
                  prefix: Padding(
                    padding: EdgeInsets.all(8),
                    child: Icon(FluentIcons.profile_search),
                  ),
                ),
              ),
              Spacer(flex: 4),
              Flexible(
                flex: 1,
                child: ListTile(
                  leading: Icon(FluentIcons.archive),
                  title: Text("Empty stock", style: TextStyle(fontSize: 14)),
                  onPressed: () {},
                ),
              ),
            ],
          ),
        ),
        Flexible(
          flex: 1,
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: CommandBar(
                  overflowBehavior: CommandBarOverflowBehavior.wrap,
                  primaryItems: [
                    CommandBarButton(
                      icon: const Icon(FluentIcons.add),
                      label: Text(lang.add),
                      tooltip: "lang.add_client_tooltip",
                      onPressed: () {
                        // Create something new!
                      },
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.edit),
                      label: Text(lang.edit),
                      tooltip: "lang.edit_client_tooltip",
                      onPressed: () {},
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.delete),
                      label: Text(lang.delete),
                      tooltip: "lang.delete_client_tooltip",
                      onPressed: () {},
                    ),
                    CommandBarSeparator(color: Colors.grey[50]),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.update_restore),
                      label: Text("Update"),
                      tooltip: "lang.client_payment_tooltip",
                      onPressed: () {},
                    ),
                  ],
                ),
              ),
              Spacer(flex: 5),
            ],
          ),
        ),
        Flexible(
          flex: 20,
          child: Row(
            spacing: 18,
            children: [
              Expanded(
                flex: 3,
                child: CardHighlight(child: ProductsTableDatagrid()),
              ),
              Expanded(
                flex: 1,
                child: CardHighlight(child: ProductsDetailsDatagrid()),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
