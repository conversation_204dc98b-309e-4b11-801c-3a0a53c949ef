import 'package:flutter_riverpod/flutter_riverpod.dart';

final searchQueryProvider =
    StateNotifierProvider.family<SearchQueryNotifier, String, String>((
      ref,
      uid,
    ) {
      return SearchQueryNotifier();
    });

class SearchQueryNotifier extends StateNotifier<String> {
  SearchQueryNotifier() : super('');

  void setQuery(String query) {
    state = query;
  }

  void clear() {
    state = '';
  }
}
