import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
// import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/cash_actual/cash_register_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class RegisterDataGrid extends StatefulWidget {
  const RegisterDataGrid({super.key});

  @override
  State<RegisterDataGrid> createState() => _RegisterDataGridState();
}

class _RegisterDataGridState extends State<RegisterDataGrid> {
  List<CashRegisterTableModel> employees = <CashRegisterTableModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    // final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'bill_number',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("BILL N°", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'client',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text("CLIENT", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'amount',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text("AMOUNT", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'time',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text("TIME", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<CashRegisterTableModel> getEmployeeData() {
    return [
      CashRegisterTableModel(
        billNumber: 1,
        client: "HAMZA HALFI",
        amount: 15000.00,
        time: DateTime.now(),
      ),
      CashRegisterTableModel(
        billNumber: 1,
        client: "HAMZA HALFI",
        amount: 15000.00,
        time: DateTime.now(),
      ),
      CashRegisterTableModel(
        billNumber: 1,
        client: "HAMZA HALFI",
        amount: 15000.00,
        time: DateTime.now(),
      ),
      CashRegisterTableModel(
        billNumber: 1,
        client: "HAMZA HALFI",
        amount: 15000.00,
        time: DateTime.now(),
      ),
      CashRegisterTableModel(
        billNumber: 1,
        client: "HAMZA HALFI",
        amount: 15000.00,
        time: DateTime.now(),
      ),
      CashRegisterTableModel(
        billNumber: 1,
        client: "HAMZA HALFI",
        amount: 15000.00,
        time: DateTime.now(),
      ),
      CashRegisterTableModel(
        billNumber: 1,
        client: "HAMZA HALFI",
        amount: 15000.00,
        time: DateTime.now(),
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<CashRegisterTableModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(
                    columnName: 'bill_number',
                    value: e.billNumber,
                  ),
                  DataGridCell<String>(columnName: 'client', value: e.client),
                  DataGridCell<double>(columnName: 'amount', value: e.amount),
                  DataGridCell<DateTime>(columnName: 'time', value: e.time),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
