import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/accounting/accounting_page.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/cash_actual/cash_actual_page.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/sms_reports/sms_reports_page.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/traceabilty/traceabilty_page.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/transactions/transactions_page.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/vat_transactions/vat_transactions_page.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class CashFlowPage extends StatefulWidget {
  const CashFlowPage({super.key});

  @override
  State<CashFlowPage> createState() => _CashFlowPageState();
}

class _CashFlowPageState extends State<CashFlowPage> {
  static List<String> contacts = [
    'Cash actual',
    'Traceabilty',
    'Accounting',
    'Transactions',
    'VAT transactions',
    'SMS Reports',
  ];
  static List<Widget> pages = [
    CashActualPage(),
    TraceabiltyPage(),
    AccountingPage(),
    TransactionsPage(),
    VatTransactionsPage(),
    SmsReportsPage(),
  ];
  ValueNotifier<String> selectedContact = ValueNotifier(contacts[0]);
  @override
  Widget build(BuildContext context) {
    return ScaffoldPage.withPadding(
      content: ValueListenableBuilder(
        valueListenable: selectedContact,
        builder: (context, value, child) {
          return Row(
            spacing: 18.0,
            children: [
              Flexible(
                flex: 1,
                child: CardHighlight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          "Main Regester",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text("Tracking"),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 18.0),
                        child: Divider(),
                      ),
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: contacts.length,
                        itemBuilder: (context, index) {
                          final contact = contacts[index];
                          return ListTile.selectable(
                            contentAlignment: CrossAxisAlignment.start,
                            selectionMode: ListTileSelectionMode.single,
                            title: Text(
                              contact,
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                                color:
                                    selectedContact.value == contact
                                        ? FluentTheme.of(context).accentColor
                                        : null,
                              ),
                            ),
                            selected: selectedContact.value == contact,
                            onSelectionChange:
                                (v) => selectedContact.value = contact,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              Flexible(
                flex: 7,
                child: pages[contacts.indexOf(selectedContact.value)],
              ),
            ],
          );
        },
      ),
    );
  }
}
