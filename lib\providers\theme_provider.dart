import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Create a StateNotifier to manage the theme state
class ThemeNotifier extends StateNotifier<ThemeMode> {
  ThemeNotifier() : super(ThemeMode.light);

  Future<void> fetchThemeMode() async {
    var prefs = await SharedPreferences.getInstance();
    int? themeMode = prefs.getInt('theme_mode');
    if (themeMode != null) {
      if (themeMode == 1) {
        state = ThemeMode.light;
      } else {
        state = ThemeMode.dark;
      }
    }
  }

  void toggleTheme() async {
    var prefs = await SharedPreferences.getInstance();
    state = state == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;
    await prefs.setInt('theme_mode', state.index);
  }

  void setTheme(ThemeMode mode) {
    state = mode;
  }
}

// Create a provider for the theme notifier
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeMode>((ref) {
  final notifier = ThemeNotifier();
  notifier.fetchThemeMode();
  return notifier;
});
