import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/presentation/pages/stock/pages/clients/clients_stock_page.dart';
import 'package:workshop_studio/presentation/pages/stock/pages/invoices/invoices_stock_page.dart';
import 'package:workshop_studio/presentation/pages/stock/pages/orders/orders_stock_page.dart';
import 'package:workshop_studio/presentation/pages/stock/pages/products/products_stock_page.dart';
import 'package:workshop_studio/presentation/pages/stock/pages/suppliers/suppliers_stock_page.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class StockPage extends StatefulWidget {
  const StockPage({super.key});

  @override
  State<StockPage> createState() => _StockPageState();
}

class _StockPageState extends State<StockPage> {
  static List<String> contacts = [
    'Products',
    'Clients',
    'Suppliers',
    'Invoices',
    'Orders',
  ];
  static List<Widget> pages = [
    ProductsStockPage(),
    ClientsStockPage(),
    SuppliersStockPage(),
    InvoicesStockPage(),
    OrdersStockPage(),
  ];
  ValueNotifier<String> selectedContact = ValueNotifier(contacts[0]);
  @override
  Widget build(BuildContext context) {
    return ScaffoldPage.withPadding(
      content: ValueListenableBuilder(
        valueListenable: selectedContact,
        builder: (context, value, child) {
          return Row(
            spacing: 18.0,
            children: [
              Flexible(
                flex: 1,
                child: CardHighlight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text(
                          "Main Stock",
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Text("Tracking"),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 18.0),
                        child: Divider(),
                      ),
                      ListView.builder(
                        shrinkWrap: true,
                        itemCount: contacts.length,
                        itemBuilder: (context, index) {
                          final contact = contacts[index];
                          return ListTile.selectable(
                            contentAlignment: CrossAxisAlignment.start,
                            selectionMode: ListTileSelectionMode.single,
                            title: Text(
                              contact,
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w400,
                                color:
                                    selectedContact.value == contact
                                        ? FluentTheme.of(context).accentColor
                                        : null,
                              ),
                            ),
                            selected: selectedContact.value == contact,
                            onSelectionChange:
                                (v) => selectedContact.value = contact,
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              Flexible(
                flex: 7,
                child: pages[contacts.indexOf(selectedContact.value)],
              ),
            ],
          );
        },
      ),
    );
  }
}
