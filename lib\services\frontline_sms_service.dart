import 'dart:async';
import 'dart:io';
import 'package:http/http.dart' as http;

/// Service for sending SMS via Frontline SMS
/// Follows the pattern from Python frontlineSms class
class FrontlineSmsService {
  static final http.Client _httpClient = http.Client();

  // Timeout configurations
  static const Duration _dnsTimeout = Duration(seconds: 5);
  static const Duration _httpTimeout = Duration(seconds: 10);
  static const Duration _totalTimeout = Duration(seconds: 20);

  /// Resolve hostname to IP address
  /// Equivalent to Python's socket.gethostbyname()
  static Future<String> _realIp(String address) async {
    try {
      final addresses = await InternetAddress.lookup(
        address,
      ).timeout(_dnsTimeout);
      if (addresses.isNotEmpty) {
        return addresses.first.address;
      }
      return address;
    } catch (e) {
      // If lookup fails or times out, return original address
      return address;
    }
  }

  /// Send SMS via Frontline SMS API
  /// Follows the exact pattern from Python code:
  /// GET request to http://{ip}:{port}/send/sms/{destination}/{message}/
  ///
  /// [address] - Frontline server address
  /// [port] - Frontline server port
  /// [destination] - Phone number to send SMS to
  /// [message] - SMS message content
  ///
  /// Returns true if SMS was sent successfully, false otherwise
  /// Throws [TimeoutException] if the operation times out
  /// Throws [Exception] with descriptive message for other errors
  static Future<bool> sendSms({
    required String address,
    required String port,
    required String destination,
    required String message,
  }) async {
    return await _sendSmsWithTimeout(
      address: address,
      port: port,
      destination: destination,
      message: message,
    ).timeout(
      _totalTimeout,
      onTimeout: () {
        throw TimeoutException(
          'SMS service timeout after ${_totalTimeout.inSeconds} seconds',
          _totalTimeout,
        );
      },
    );
  }

  /// Internal method to send SMS with individual timeouts
  static Future<bool> _sendSmsWithTimeout({
    required String address,
    required String port,
    required String destination,
    required String message,
  }) async {
    try {
      // Resolve hostname to IP address (equivalent to self.real_ip(address))
      final ipAddress = await _realIp(address);

      // URL encode the message to handle special characters
      final encodedMessage = Uri.encodeComponent(message);

      // Construct the Frontline SMS API URL following Python pattern
      final url = Uri.parse(
        'http://$ipAddress:$port/send/sms/$destination/$encodedMessage/',
      );

      // Send the HTTP GET request with timeout (matching Python's request method)
      final response = await _httpClient.get(url).timeout(_httpTimeout);

      // Check if the request was successful (status == 200)
      if (response.statusCode == 200) {
        return true;
      } else {
        throw Exception(
          'SMS service returned status code: ${response.statusCode}',
        );
      }
    } on TimeoutException catch (e) {
      throw TimeoutException('SMS service timeout: ${e.message}', e.duration);
    } on SocketException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('SMS service error: ${e.toString()}');
    }
  }

  /// Validate phone number format
  static bool isValidPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');

    // Check if the number has at least 8 digits and at most 15 digits
    return cleanNumber.length >= 8 && cleanNumber.length <= 15;
  }

  /// Format phone number for SMS sending
  static String formatPhoneNumber(String phoneNumber) {
    // Remove any non-digit characters except +
    String formatted = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');

    // If number doesn't start with +, add country code if needed
    if (!formatted.startsWith('+')) {
      // You might want to add default country code here
      // For now, just return as is
      return formatted;
    }

    return formatted;
  }
}
