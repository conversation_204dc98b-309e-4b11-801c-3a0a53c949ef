import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/models/client/client_sold_model.dart';
import 'package:workshop_studio/models/device/device_model.dart';
import 'package:workshop_studio/models/client/client_soldtva_model.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';
import 'package:workshop_studio/services/database_service.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';

class ClientTransactionsState {
  final List<ClientSoldModel> transactions;
  final List<ClientSoldTvaModel> transactionsTva;
  final List<DeviceModel> devices;
  final bool isLoading;
  final ClientSoldModel? selectedTransaction;
  final DeviceModel? selectedDevice;

  ClientTransactionsState({
    required this.transactions,
    required this.transactionsTva,
    required this.devices,
    required this.isLoading,
    this.selectedTransaction,
    this.selectedDevice,
  });

  ClientTransactionsState copyWith({
    List<ClientSoldModel>? transactions,
    List<ClientSoldTvaModel>? transactionsTva,
    List<DeviceModel>? devices,
    bool? isLoading,
    ClientSoldModel? selectedTransaction,
    DeviceModel? selectedDevice,
  }) {
    return ClientTransactionsState(
      transactions: transactions ?? this.transactions,
      transactionsTva: transactionsTva ?? this.transactionsTva,
      devices: devices ?? this.devices,
      isLoading: isLoading ?? this.isLoading,
      selectedTransaction: selectedTransaction ?? this.selectedTransaction,
      selectedDevice: selectedDevice ?? this.selectedDevice,
    );
  }
}

final selectedTransactionProvider = StateProvider<ClientSoldModel?>(
  (ref) => null,
);
final selectedTransactionTvaProvider = StateProvider<ClientSoldTvaModel?>(
  (ref) => null,
);

final clientTransactionsProvider = StateNotifierProvider.family<
  ClientTransactionsNotifier,
  ClientTransactionsState,
  ClientModel
>((ref, client) {
  final db = ref.read(mysqlServiceProvider);
  return ClientTransactionsNotifier(db, ref: ref, client: client);
});

class ClientTransactionsNotifier
    extends StateNotifier<ClientTransactionsState> {
  final MySQLService _dbService;
  final Ref ref;
  final ClientModel client;

  ClientTransactionsNotifier(
    this._dbService, {
    required this.ref,
    required this.client,
  }) : super(
         ClientTransactionsState(
           transactions: [],
           transactionsTva: [],
           devices: [],
           isLoading: false,
         ),
       ) {
    loadTransactions();
    loadDevices();
  }

  Future<void> loadTransactions() async {
    // f"SELECT client_soldtva.id, clients.c_name, concat(CAST(client_soldtva.tht AS CHAR), ' {self.currency}'), "
    //                 f"concat(CAST(client_soldtva.tvav AS CHAR), '  %'), concat(CAST(client_soldtva.ttva AS CHAR),  ' {self.currency}'), "
    //                 f"concat(CAST(client_soldtva.ttc AS CHAR), ' {self.currency}'), client_soldtva.payment_type, "
    //                 f"DATE_FORMAT(client_soldtva.date, '%Y-%m-%d'), client_soldtva.time, DATE_FORMAT(client_soldtva.date_warranty, '%d/%m/%Y'), client_soldtva.note "
    //                 f"FROM client_soldtva JOIN clients ON client_soldtva.c_id = clients.id WHERE client_soldtva.c_id = {self.customer_id}"

    final query =
        client.cTva
            ? '''
        SELECT 
          id, c_id, machines_ids, payment_type,
          tht, tvav, ttva, ttc,
          note, date, time, date_warranty
        FROM client_soldtva 
        WHERE c_id = :clientId 
        ORDER BY id DESC 
      '''
            : '''
        SELECT 
          s_id, c_id, machines_ids, payment_type,
          current_balance, payment, old_balance, new_balance,
          note, date, time, date_warranty
        FROM client_sold 
        WHERE c_id = :clientId 
        ORDER BY s_id DESC 
      ''';

    final results = await _dbService.query(
      query,
      params: {'clientId': client.id},
    );
    if (client.cTva) {
      try {
        final transactionsTva =
            results.rows
                .map<ClientSoldTvaModel>(
                  (row) => ClientSoldTvaModel.fromJson(row.typedAssoc()),
                )
                .toList();
        state = state.copyWith(transactionsTva: transactionsTva);
      } catch (e) {
        return;
      }
    } else {
      final transactions =
          results.rows
              .map<ClientSoldModel>(
                (row) => ClientSoldModel.fromJson(row.typedAssoc()),
              )
              .toList();

      state = state.copyWith(transactions: transactions);
    }
  }

  Future<void> loadDevices() async {
    final results = await _dbService.query(
      'SELECT * FROM client_machines WHERE c_id = :clientId ORDER BY date DESC',
      params: {'clientId': client.id},
    );
    final devices =
        results.rows
            .map<DeviceModel>((row) => DeviceModel.fromJson(row.typedAssoc()))
            .toList();
    state = state.copyWith(devices: devices);
  }
}
