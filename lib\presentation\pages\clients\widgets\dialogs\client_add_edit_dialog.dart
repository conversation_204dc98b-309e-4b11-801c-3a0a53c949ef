import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:workshop_studio/providers/clients/client/client_crud_provider.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/presentation/utils/upper_case_formatter.dart';
import 'package:flutter/services.dart';

void showClientAddEditDialog(
  BuildContext context,
  WidgetRef ref,
  ClientModel? client,
  bool isEdit,
) {
  final lang = AppLocalizations.of(context);
  // Initialize controllers with client data
  final nameController = TextEditingController(text: client?.cName ?? '');
  final addressController = TextEditingController(text: client?.cAddress ?? '');
  final phone1Controller = TextEditingController(text: client?.cPhone ?? '');
  final phone2Controller = TextEditingController(text: client?.cPhone2 ?? '');
  final debtController = TextEditingController(
    text: client?.debts.toString() ?? '0.0',
  );
  final emailController = TextEditingController(text: client?.cEmail ?? '');
  final rcController = TextEditingController(text: client?.cRc ?? '');
  final nifController = TextEditingController(text: client?.cNif ?? '');
  final aiController = TextEditingController(text: client?.cAi ?? '');
  final nisController = TextEditingController(text: client?.cNis ?? '');

  List<String> clientTypes = [lang.dealer, lang.particular, lang.company];
  // Initialize other fields
  int typeIndex = client?.cType ?? 1;
  if (typeIndex < 0 || typeIndex >= clientTypes.length) {
    typeIndex = 1;
  }
  String clientType = clientTypes[typeIndex];
  String clientCity =
      citys.contains(client?.cCity)
          ? client?.cCity ?? citys.first
          : citys.first;
  bool subjectToVAT = client?.cTva ?? false;

  // Remove: final isEdit = client != null;

  final dialogWidth = MediaQuery.of(context).size.width / 2;
  final dialogHeight = MediaQuery.of(context).size.height / 1.65;

  late bool inProgress = false;

  showDialog(
    context: context,
    builder: (context) {
      return StatefulBuilder(
        builder: (context, setState) {
          Widget buildField(String label, Widget field) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 6),
              child: Row(
                children: [
                  Expanded(flex: 1, child: Text(label)),
                  Expanded(flex: 3, child: field),
                ],
              ),
            );
          }

          return ContentDialog(
            constraints: BoxConstraints.expand(
              width: dialogWidth,
              height: dialogHeight,
            ),
            title: Text(
              isEdit ? 'Edit Client' : 'Add New Client',
              style: TextStyle(fontSize: 18),
            ),
            content: ScaffoldPage.scrollable(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Left Column
                    Expanded(
                      child: CardHighlight(
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            buildField(
                              'Client Type',
                              ComboBox<String>(
                                isExpanded: true,
                                value: clientType,
                                items: List.generate(
                                  clientTypes.length,
                                  (index) => ComboBoxItem(
                                    value: clientTypes[index],
                                    child: Text(clientTypes[index]),
                                  ),
                                ),
                                onChanged:
                                    (value) =>
                                        setState(() => clientType = value!),
                              ),
                            ),
                            buildField(
                              'Client Name',
                              TextBox(
                                padding: EdgeInsets.all(10),
                                controller: nameController,
                                placeholder: 'Enter name',
                                inputFormatters: [UpperCaseTextFormatter()],
                              ),
                            ),
                            buildField(
                              'Client City',
                              ComboBox<String>(
                                isExpanded: true,
                                value: clientCity,
                                items: List.generate(
                                  citys.length,
                                  (index) => ComboBoxItem(
                                    value: citys[index],
                                    child: Text(citys[index]),
                                  ),
                                ),
                                onChanged:
                                    (value) =>
                                        setState(() => clientCity = value!),
                              ),
                            ),
                            buildField(
                              'Address',
                              TextBox(
                                padding: EdgeInsets.all(10),
                                controller: addressController,
                                placeholder: 'Enter address',
                                inputFormatters: [UpperCaseTextFormatter()],
                              ),
                            ),
                            buildField(
                              'Phone 1',
                              TextBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontWeight: FontWeight.w500,
                                ),
                                padding: EdgeInsets.all(10),
                                controller: phone1Controller,
                                placeholder: 'Primary phone',
                                keyboardType: TextInputType.phone,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                              ),
                            ),
                            buildField(
                              'Phone 2',
                              TextBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontWeight: FontWeight.w500,
                                ),
                                padding: EdgeInsets.all(10),
                                controller: phone2Controller,
                                placeholder: 'Secondary phone',
                                keyboardType: TextInputType.phone,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                              ),
                            ),
                            buildField(
                              'Debts',
                              TextBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontWeight: FontWeight.w500,
                                ),
                                padding: EdgeInsets.all(10),
                                controller: debtController,
                                placeholder: 'Debt amount',
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                    RegExp(r'^\d+\.?\d*'),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    // Right Column
                    Expanded(
                      child: CardHighlight(
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            buildField(
                              'Email',
                              TextBox(
                                padding: EdgeInsets.all(10),
                                controller: emailController,
                                placeholder: 'Enter email',
                              ),
                            ),
                            buildField(
                              'RC',
                              TextBox(
                                padding: EdgeInsets.all(10),
                                controller: rcController,
                                placeholder: 'Enter RC',
                                inputFormatters: [UpperCaseTextFormatter()],
                              ),
                            ),
                            buildField(
                              'NIF',
                              TextBox(
                                padding: EdgeInsets.all(10),
                                controller: nifController,
                                placeholder: 'Enter NIF',
                                inputFormatters: [UpperCaseTextFormatter()],
                              ),
                            ),
                            buildField(
                              'AI',
                              TextBox(
                                padding: EdgeInsets.all(10),
                                controller: aiController,
                                placeholder: 'Enter AI',
                                inputFormatters: [UpperCaseTextFormatter()],
                              ),
                            ),
                            buildField(
                              'NIS',
                              TextBox(
                                padding: EdgeInsets.all(10),
                                controller: nisController,
                                placeholder: 'Enter NIS',
                                inputFormatters: [UpperCaseTextFormatter()],
                              ),
                            ),
                            const SizedBox(height: 20),
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Checkbox(
                                    content: Text('Subject to VAT'),
                                    checked: subjectToVAT,
                                    onChanged: (value) {
                                      setState(
                                        () => subjectToVAT = value ?? false,
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                            SizedBox(height: 50),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              Button(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: const Text('Cancel'),
                ),
                onPressed: () => Navigator.pop(context),
              ),
              FilledButton(
                child: Padding(
                  padding: EdgeInsets.all(inProgress ? 2.0 : 8.0),
                  child:
                      inProgress
                          ? SizedBox(
                            height: 28,
                            width: 28,
                            child: ProgressRing(),
                          )
                          : Text(
                            isEdit ? 'Update' : 'Create',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                ),
                // Update the submit button onPressed
                onPressed: () async {
                  final crud = ref.read(clientCrudProvider);
                  setState(() => inProgress = true);
                  try {
                    final newClient = ClientModel(
                      id: client?.id ?? 0, // Will be auto-generated
                      cName: nameController.text,
                      cCity: clientCity,
                      cAddress: addressController.text,
                      cPhone: phone1Controller.text,
                      cPhone2: phone2Controller.text,
                      cType: clientTypes.indexOf(clientType),
                      date: DateTime.now().toString(),
                      time: DateFormat('HH:mm:ss').format(DateTime.now()),
                      debts: double.tryParse(debtController.text) ?? 0,
                      user: 'current_user', // Replace with actual user
                      cEmail: emailController.text,
                      cRc: rcController.text,
                      cNif: nifController.text,
                      cAi: aiController.text,
                      cNis: nisController.text,
                      cTva: subjectToVAT,
                    );
                    isEdit
                        ? await crud.updateClient(newClient)
                        : await crud.createClient(newClient);
                    ref.read(clientProvider.notifier).loadClients();
                    // ignore: use_build_context_synchronously
                    Navigator.pop(context);
                    await displayInfoBar(
                      // ignore: use_build_context_synchronously
                      context,
                      builder: (context, close) {
                        return InfoBar(
                          title: Text(
                            isEdit ? "Client updated" : "Client Added",
                          ),
                          content: Text(
                            isEdit
                                ? "Client ${nameController.text} has been updated successfully"
                                : "New client ${nameController.text} has been created successfully",
                          ),
                          action: IconButton(
                            icon: const Icon(FluentIcons.clear),
                            onPressed: close,
                          ),
                          severity: InfoBarSeverity.success,
                        );
                      },
                    );
                  } catch (e) {
                    await displayInfoBar(
                      // ignore: use_build_context_synchronously
                      context,
                      builder: (context, close) {
                        return InfoBar(
                          title: Text("Error"),
                          content: Text(e.toString()),
                          action: IconButton(
                            icon: const Icon(FluentIcons.clear),
                            onPressed: close,
                          ),
                          severity: InfoBarSeverity.error,
                        );
                      },
                    );
                  }
                  setState(() => inProgress = false);
                },
              ),
            ],
          );
        },
      );
    },
  );
}

final List<String> citys = [
  "Adrar",
  "Chlef",
  "Laghouat",
  "Oum el-Bouaghi",
  "Batna",
  "Béjaïa",
  "Biskra",
  "Béchar",
  "Blida",
  "Bouïra",
  "Tamanghasset",
  "Tébessa",
  "Tlemcen",
  "Tiaret",
  "Tizi Ouzou",
  "Algiers",
  "Djelfa",
  "Jijel",
  "Sétif",
  "Saïda",
  "Skikda",
  "Sidi Bel Abbès",
  "Annaba",
  "Guelma",
  "Constantine",
  "Médéa",
  "Mostaganem",
  "M'sila",
  "Mascara",
  "Ouargla",
  "Oran",
  "El Bayadh",
  "Illizi",
  "Bordj Bou Arréridj",
  "Boumerdès",
  "El Taref",
  "Tindouf",
  "Tissemsilt",
  "El Oued",
  "Khenchela",
  "Souk Ahras",
  "Tipaza",
  "Mila",
  "Aïn Defla",
  "Naâma",
  "Aïn Témouchent",
  "Ghardaïa",
  "Relizane",
  "El M'ghair",
  "El Menia",
  "Ouled Djellal",
  "Bordj Baji Mokhtar",
  "Béni Abbès",
  "Timimoun",
  "Touggourt",
  "Djanet",
  "In Salah",
  "In Guezzam",
];
