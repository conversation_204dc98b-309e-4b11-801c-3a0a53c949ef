// timeline_filter_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';

enum TimelineFilterType {
  all,
  currentYear,
  currentMonth,
  currentWeek,
  customRange,
}

class TimelineFilter {
  final TimelineFilterType type;
  final DateTime? from;
  final DateTime? to;

  const TimelineFilter({required this.type, this.from, this.to});

  TimelineFilter copyWith({
    TimelineFilterType? type,
    DateTime? from,
    DateTime? to,
    String? searchQuery,
  }) {
    return TimelineFilter(
      type: type ?? this.type,
      from: from ?? this.from,
      to: to ?? this.to,
    );
  }
}

class TimelineFilterNotifier extends StateNotifier<TimelineFilter> {
  TimelineFilterNotifier()
    : super(TimelineFilter(type: TimelineFilterType.currentYear));

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setAll() {
    state = TimelineFilter(type: TimelineFilterType.all);
  }

  void setCurrentYear() {
    state = TimelineFilter(type: TimelineFilterType.currentYear);
  }

  void setCurrentMonth() {
    state = TimelineFilter(type: TimelineFilterType.currentMonth);
  }

  void setCurrentWeek() {
    state = TimelineFilter(type: TimelineFilterType.currentWeek);
  }

  void setCustomRange(DateTime from, DateTime to) {
    state = TimelineFilter(
      type: TimelineFilterType.customRange,
      from: from,
      to: to,
    );
  }

  void setFrom(DateTime from) {
    state = state.copyWith(from: from);
  }

  void setTo(DateTime to) {
    state = state.copyWith(to: to);
  }

  void applyCustomRange() {
    if (state.from != null && state.to != null) {
      state = state.copyWith(type: TimelineFilterType.customRange);
    }
  }

  void setFilter(TimelineFilterType type) {
    final now = DateTime.now();
    DateTime? from;
    DateTime? to;

    switch (type) {
      case TimelineFilterType.all:
        from = null;
        to = null;
        break;
      case TimelineFilterType.currentYear:
        from = DateTime(now.year);
        to = DateTime(now.year + 1).subtract(Duration(days: 1));
        break;
      case TimelineFilterType.currentMonth:
        from = DateTime(now.year, now.month);
        to = DateTime(now.year, now.month + 1).subtract(Duration(days: 1));
        break;
      case TimelineFilterType.currentWeek:
        final start = now.subtract(Duration(days: now.weekday - 1));
        from = DateTime(start.year, start.month, start.day);
        to = from.add(Duration(days: 6));
        break;
      case TimelineFilterType.customRange:
        // Use current state values
        from = state.from;
        to = state.to;
        break;
    }

    state = TimelineFilter(type: type, from: from, to: to);
  }
}

final timelineFilterProviderFamily = StateNotifierProvider.family<
  TimelineFilterNotifier,
  TimelineFilter,
  String
>((ref, uid) {
  return TimelineFilterNotifier();
});
