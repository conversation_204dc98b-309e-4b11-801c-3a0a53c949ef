import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/sms_reports/sms_reports_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class SmsReportsDatagrid extends StatefulWidget {
  const SmsReportsDatagrid({super.key});

  @override
  State<SmsReportsDatagrid> createState() => _SmsReportsDatagridState();
}

class _SmsReportsDatagridState extends State<SmsReportsDatagrid> {
  List<SmsReportsTableModel> employees = <SmsReportsTableModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'id',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.id, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'client',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.client, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'device',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("DEVICE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'type',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("SMS TYPE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'date',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.date, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'time',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.time, style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<SmsReportsTableModel> getEmployeeData() {
    return [
      SmsReportsTableModel(
        id: 1,
        client: "HAMZA HALFI",
        device: "APPLE MACBOOK PRO 13",
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
      ),
      SmsReportsTableModel(
        id: 1,
        client: "HAMZA HALFI",
        device: "APPLE MACBOOK PRO 13",
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
      ),
      SmsReportsTableModel(
        id: 1,
        client: "HAMZA HALFI",
        device: "APPLE MACBOOK PRO 13",
        type: 1,
        date: DateTime.now(),
        time: DateTime.now(),
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<SmsReportsTableModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'id', value: e.id),
                  DataGridCell<String>(columnName: 'client', value: e.client),
                  DataGridCell<String>(columnName: 'device', value: e.device),
                  DataGridCell<int>(columnName: 'type', value: e.type),
                  DataGridCell<DateTime>(columnName: 'date', value: e.date),
                  DataGridCell<DateTime>(columnName: 'time', value: e.time),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
