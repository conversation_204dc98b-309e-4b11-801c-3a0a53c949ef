class ClientSoldModel {
  final int? sId;
  final int cId;
  final int? bId;
  final dynamic machinesIds; // JSON type, can be List or Map depending on usage
  final int paymentType;
  final double currentBalance;
  final double payment;
  final double oldBalance;
  final double newBalance;
  final String? note;
  final String date;
  final String time;
  final String? dateWarranty;

  ClientSoldModel({
    this.sId,
    required this.cId,
    this.bId,
    this.machinesIds,
    required this.paymentType,
    required this.currentBalance,
    required this.payment,
    required this.oldBalance,
    required this.newBalance,
    this.note,
    required this.date,
    required this.time,
    this.dateWarranty,
  });

  factory ClientSoldModel.fromJson(Map<String, dynamic> json) {
    return ClientSoldModel(
      sId: json['s_id'] != null ? int.tryParse(json['s_id'].toString()) : null,
      cId: int.parse(json['c_id'].toString()),
      bId: json['b_id'] != null ? int.tryParse(json['b_id'].toString()) : null,
      machinesIds: json['machines_ids'],
      paymentType: int.parse(json['payment_type'].toString()),
      currentBalance: double.parse(json['current_balance'].toString()),
      payment: double.parse(json['payment'].toString()),
      oldBalance: double.parse(json['old_balance'].toString()),
      newBalance: double.parse(json['new_balance'].toString()),
      note: json['note'] as String?,
      date: json['date'] as String,
      time: json['time'] as String,
      dateWarranty: json['date_warranty'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      's_id': sId,
      'c_id': cId,
      'b_id': bId,
      'machines_ids': machinesIds,
      'payment_type': paymentType,
      'current_balance': currentBalance,
      'payment': payment,
      'old_balance': oldBalance,
      'new_balance': newBalance,
      'note': note,
      'date': date,
      'time': time,
      'date_warranty': dateWarranty,
    };
  }
}
