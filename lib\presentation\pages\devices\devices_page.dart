import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/devices/tabs/delevered_devices.dart';
import 'package:workshop_studio/presentation/pages/devices/tabs/devices_at_workshop.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/delevered_devices_timeline.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/devices_at_workshop_timeline.dart';

class DevicesPage extends StatefulWidget {
  const DevicesPage({super.key});

  @override
  State<DevicesPage> createState() => _DevicesPageState();
}

class _DevicesPageState extends State<DevicesPage> {
  int currentIndex = 0;
  List<Tab> tabs = [];

  @override
  Widget build(BuildContext context) {
    final theme = FluentTheme.of(context);
    final lang = AppLocalizations.of(context);
    return ScaffoldPage.withPadding(
      content: TabView(
        tabs: [
          Tab(
            selectedBackgroundColor: WidgetStatePropertyAll(
              theme.micaBackgroundColor,
            ),
            outlineColor: WidgetStatePropertyAll(
              theme.resources.controlStrokeColorSecondary,
            ),
            icon: Icon(CarbonIcons.workspace, size: 24),
            text: Text(lang.devices_at_workshop),
            body: Container(
              padding: EdgeInsets.all(18.0),
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.resources.controlStrokeColorSecondary,
                ),
              ),
              child: Row(
                spacing: 20.0,
                children: [
                  Flexible(flex: 1, child: DevicesAtWorkshopTimeline()),
                  Flexible(flex: 6, child: DevicesAtWorkshop()),
                ],
              ),
            ),
          ),
          Tab(
            selectedBackgroundColor: WidgetStatePropertyAll(
              theme.micaBackgroundColor,
            ),
            outlineColor: WidgetStatePropertyAll(
              theme.resources.controlStrokeColorSecondary,
            ),
            icon: Icon(CarbonIcons.delivery_parcel, size: 24),
            text: Text(lang.delivered_devices),
            body: Container(
              padding: EdgeInsets.all(18.0),
              decoration: BoxDecoration(
                border: Border.all(
                  color: theme.resources.controlStrokeColorSecondary,
                ),
              ),
              child: Row(
                spacing: 20.0,
                children: [
                  Flexible(flex: 1, child: DeleveredDevicesTimeline()),
                  Flexible(flex: 6, child: DeleveredDevices()),
                ],
              ),
            ),
          ),
        ],
        currentIndex: currentIndex,
        onChanged: (index) => setState(() => currentIndex = index),
        tabWidthBehavior: TabWidthBehavior.equal,
        showScrollButtons: false,
        onReorder: (oldIndex, newIndex) {
          setState(() {
            if (oldIndex < newIndex) {
              newIndex -= 1;
            }
            final item = tabs.removeAt(oldIndex);
            tabs.insert(newIndex, item);

            if (currentIndex == newIndex) {
              currentIndex = oldIndex;
            } else if (currentIndex == oldIndex) {
              currentIndex = newIndex;
            }
          });
        },
      ),
    );
  }
}
