import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/traceabilty/traceabilty_delivered_device_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class DeliveredDevicesDatagrid extends StatefulWidget {
  const DeliveredDevicesDatagrid({super.key});

  @override
  State<DeliveredDevicesDatagrid> createState() =>
      _DeliveredDevicesDatagridState();
}

class _DeliveredDevicesDatagridState extends State<DeliveredDevicesDatagrid> {
  List<TraceabiltyDeliveredDeviceModel> employees =
      <TraceabiltyDeliveredDeviceModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'type',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.type.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'brand',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.brand.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'serei',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.serie.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'model',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.model.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'price',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text("PRICE", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<TraceabiltyDeliveredDeviceModel> getEmployeeData() {
    return [
      TraceabiltyDeliveredDeviceModel(
        type: 2,
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "CQ58",
        price: 6000.00,
      ),
      TraceabiltyDeliveredDeviceModel(
        type: 2,
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "CQ58",
        price: 6000.00,
      ),
      TraceabiltyDeliveredDeviceModel(
        type: 2,
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "CQ58",
        price: 6000.00,
      ),
      TraceabiltyDeliveredDeviceModel(
        type: 2,
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "CQ58",
        price: 6000.00,
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({
    required List<TraceabiltyDeliveredDeviceModel> employeeData,
  }) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'type', value: e.type),
                  DataGridCell<String>(columnName: 'time', value: e.brand),
                  DataGridCell<String>(columnName: 'serei', value: e.serei),
                  DataGridCell<String>(columnName: 'model', value: e.model),
                  DataGridCell<double>(columnName: 'price', value: e.price),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
