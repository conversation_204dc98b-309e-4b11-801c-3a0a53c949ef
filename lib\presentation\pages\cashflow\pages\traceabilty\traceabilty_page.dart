import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/traceabilty/widgets/cash_register_datagrid.dart';
// import 'package:workshop_studio/presentation/pages/cashflow/pages/traceabilty/widgets/cash_register_timeline.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/traceabilty/widgets/delivered_devices_datagrid.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/traceabilty/widgets/receipt_list_register_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class TraceabiltyPage extends StatelessWidget {
  const TraceabiltyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return Column(
      spacing: 18.0,
      children: [
        Row(
          children: [
            Flexible(
              flex: 1,
              child: TextBox(
                expands: false,
                placeholder: lang.search,
                prefix: Padding(
                  padding: EdgeInsets.all(8),
                  child: Icon(FluentIcons.profile_search),
                ),
              ),
            ),
            Flexible(flex: 3, child: SizedBox()),
          ],
        ),
        Expanded(
          child: Row(
            spacing: 18.0,
            children: [
              Flexible(
                flex: 2,
                child: Column(
                  spacing: 18,
                  children: [
                    CardHighlight(child: Text("Cash register")),
                    Expanded(
                      child: CardHighlight(child: CashRegisterDatagrid()),
                    ),
                  ],
                ),
              ),
              Flexible(
                flex: 1,
                child: Column(
                  spacing: 18.0,
                  children: [
                    Flexible(
                      flex: 1,
                      child: Column(
                        spacing: 18,
                        children: [
                          CardHighlight(child: Text("Receipts register")),
                          Expanded(
                            child: CardHighlight(
                              child: ReceiptListRegisterDatagrid(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Flexible(
                      flex: 1,
                      child: Column(
                        spacing: 18,
                        children: [
                          CardHighlight(child: Text("Delivered devices")),
                          Expanded(
                            child: CardHighlight(
                              child: DeliveredDevicesDatagrid(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // Flexible(flex: 1, child: CashRegisterTimeline()),
            ],
          ),
        ),
      ],
    );
  }
}
