/***************************/
/*      Core Tables        */
/***************************/
CREATE TABLE IF NOT EXISTS `users`(
    `user_id` INT NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL,
    `password` VARCHAR(255) NOT NULL,
    PRIMARY KEY (`user_id`)
) ENGINE=INNODB;

CREATE TABLE IF NOT EXISTS `user_permission`(
    `id` INT NOT NULL AUTO_INCREMENT,
    `user_id` INT NOT NULL,
    `dashboard` TINYINT NOT NULL,
    `customers_machines_status` TINYINT NOT NULL,
    -- ... (113 permission columns) ...
    PRIMARY KEY (`id`),
    FOREIGN KEY (`user_id`) REFERENCES users(`user_id`)
) ENGINE=INNODB;

/***************************/
/*   Workshop Configuration */
/***************************/
CREATE TABLE IF NOT EXISTS `sobersys`(
    `id` INT NOT NULL AUTO_INCREMENT,
    `user` VARCHAR(50) NOT NULL,
    `password` VARCHAR(50) NOT NULL,
    `workshop_id` VARCHAR(50) NOT NULL,
    PRIMARY KEY(`id`)
) ENGINE=INNODB;

CREATE TABLE IF NOT EXISTS `frontline`(
    `id` INT NOT NULL AUTO_INCREMENT,
    `address` VARCHAR(50) NOT NULL DEFAULT 'localhost',
    `port` VARCHAR(50) NOT NULL DEFAULT '8011',
    PRIMARY KEY(`id`)
) ENGINE=INNODB;

/***************************/
/*    Client Management    */
/***************************/
CREATE TABLE IF NOT EXISTS clients(
    `id` INT NOT NULL AUTO_INCREMENT,
    `c_name` VARCHAR(45) NOT NULL,
    `c_city` VARCHAR(45),
    `c_address` VARCHAR(45),
    `c_phone` VARCHAR(45) NOT NULL,
    `c_phone_2` VARCHAR(45) NOT NULL,
    `c_type` INT NOT NULL,
    `date` DATE NOT NULL,
    `time` TIME NOT NULL,
    `debts` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    `user` VARCHAR(50) NOT NULL,
    `c_email` VARCHAR(45),
    `c_rc` VARCHAR(45),
    `c_nif` VARCHAR(45),
    `c_ai` VARCHAR(45),
    `c_nis` VARCHAR(45),
    `c_tva` TINYINT DEFAULT 0,
    PRIMARY KEY(id)
) ENGINE=INNODB;

CREATE TABLE IF NOT EXISTS more_contact(
    `id` INT NOT NULL AUTO_INCREMENT,
    `c_id` INT NOT NULL,
    `full_name` VARCHAR(45) NOT NULL,
    `job_title` VARCHAR(45),
    `phone_n` VARCHAR(45) NOT NULL,
    PRIMARY KEY(`id`),
    FOREIGN KEY(c_id) REFERENCES clients(id)
) ENGINE=INNODB;

/***************************/
/*   Machine Management    */
/***************************/
CREATE TABLE IF NOT EXISTS client_machines(
    `m_id` INT NOT NULL AUTO_INCREMENT,
    `c_id` INT NOT NULL,
    `b_id` INT NOT NULL,
    `m_type` VARCHAR(50) NOT NULL,
    `m_brand` VARCHAR(50) NOT NULL,
    `m_serie` VARCHAR(50),
    `m_model` VARCHAR(50),
    `m_serial_num` VARCHAR(50),
    `m_problem` VARCHAR(100),
    `m_estimated_price` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    `price` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    `warranty` TINYINT DEFAULT 0,
    `emergency` TINYINT DEFAULT 0,
    `technician` VARCHAR(50) NOT NULL,
    `deadline` INT UNSIGNED DEFAULT 0,
    `note` VARCHAR(500),
    `date` DATE NOT NULL,
    `time` TIME NOT NULL,
    `phase` INT NOT NULL,
    `dateupdate` DATE,
    `delivery` TINYINT DEFAULT 0,
    `datedelivery` DATE,
    `calculated` TINYINT NOT NULL DEFAULT 0,
    PRIMARY KEY(m_id),
    FOREIGN KEY(b_id) REFERENCES clients_bills(id)
) ENGINE=INNODB;

/***************************/
/*  Financial Transactions */
/***************************/
CREATE TABLE IF NOT EXISTS client_sold(
    `s_id` INT NOT NULL AUTO_INCREMENT,
    `c_id` INT NOT NULL,
    `b_id` INT,
    `machines_ids` JSON,
    `payment_type` INT NOT NULL,
    `current_balance` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    `payment` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    `old_balance` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    `new_balance` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    `note` TEXT,
    `date` DATE NOT NULL,
    `time` TIME NOT NULL,
    `date_warranty` DATE,
    PRIMARY KEY(s_id),
    FOREIGN KEY(c_id) REFERENCES clients(id)
) ENGINE=INNODB;

/***************************/
/*  Inventory & Products   */
/***************************/
CREATE TABLE IF NOT EXISTS `products` (
    `p_id` INT NOT NULL AUTO_INCREMENT,
    `brand` VARCHAR(50) NOT NULL,
    `product_family` VARCHAR(50) NOT NULL,
    `reference` TEXT,
    `product_designation` VARCHAR(50) NOT NULL,
    `part_number` VARCHAR(50) NOT NULL,
    `supplier_id` INT,
    `quantity` INT NOT NULL,
    `quantity_alert` INT NOT NULL,
    `purchase_price` DECIMAL(12,2) NOT NULL,
    `cump` DECIMAL(12,2) NOT NULL,
    `retail_price` DECIMAL(12,2) NOT NULL,
    `reseller_price` DECIMAL(12,2) NOT NULL,
    `product_picture` LONGBLOB,
    `technician` VARCHAR(50) NOT NULL,
    `date` DATE NOT NULL,
    PRIMARY KEY (p_id)
) ENGINE=INNODB;

/***************************/
/*     SMS Templates       */
/***************************/
CREATE TABLE IF NOT EXISTS `coupon` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `workshop_name` VARCHAR(50),
    `address` TINYTEXT,
    `tlf_fax` TINYTEXT,
    `mobil` TINYTEXT,
    `email` TINYTEXT,
    `website` TINYTEXT,
    `conditions` TEXT NOT NULL,
    PRIMARY KEY(id)
) ENGINE=INNODB;

CREATE TABLE IF NOT EXISTS `repaired_sms` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `repaired` MEDIUMTEXT,
    PRIMARY KEY(id)
) ENGINE=INNODB;

/***************************/
/*  Additional Tables      */
/***************************/
-- Workers & Activity --
CREATE TABLE IF NOT EXISTS `workers`(
    `id` INT NOT NULL AUTO_INCREMENT,
    `first_name` VARCHAR(50) NOT NULL,
    `last_name` VARCHAR(50) NOT NULL,
    `job_title` VARCHAR(50) NOT NULL,
    `joining_date` DATE NOT NULL,
    `salary` DECIMAL(12,2) NOT NULL DEFAULT 0.00,
    PRIMARY KEY(`id`)
) ENGINE=INNODB;

CREATE TABLE IF NOT EXISTS `activity`(
    `w_id` INT NOT NULL,
    `c_id` INT NOT NULL,
    `m_id` INT NOT NULL,
    `date` DATE,
    FOREIGN KEY (w_id) REFERENCES workers(id) ON DELETE CASCADE
) ENGINE=INNODB;

-- Machine Metadata --
CREATE TABLE IF NOT EXISTS `machine_type` (`m_type` VARCHAR(50) NOT NULL);
CREATE TABLE IF NOT EXISTS `machine_brand` (`m_brand` VARCHAR(50) NOT NULL);
CREATE TABLE IF NOT EXISTS `machine_serie` (`m_serie` VARCHAR(50) NOT NULL);
CREATE TABLE IF NOT EXISTS `machine_model` (`m_model` VARCHAR(50) NOT NULL);
CREATE TABLE IF NOT EXISTS `machine_problem` (`m_problem` VARCHAR(50) NOT NULL);

-- Remarks Table --
CREATE TABLE IF NOT EXISTS `repaired_remarks` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `m_id` INT NOT NULL,
    `remarks` TEXT,
    PRIMARY KEY(id),
    FOREIGN KEY(m_id) REFERENCES client_machines(m_id)
) ENGINE=INNODB;

/***************************/
/*     SMS & History       */
/***************************/
CREATE TABLE IF NOT EXISTS `sms_status` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `c_id` INT NOT NULL,
    `m_id` INT NOT NULL,
    `sms_type` INT NOT NULL,
    `date` DATE NOT NULL,
    `time` TIME NOT NULL,
    PRIMARY KEY(id),
    FOREIGN KEY(c_id) REFERENCES clients(id),
    FOREIGN KEY(m_id) REFERENCES client_machines(m_id)
) ENGINE=INNODB;

CREATE TABLE IF NOT EXISTS `machine_historic` (
    `id` INT NOT NULL AUTO_INCREMENT,
    `m_id` INT NOT NULL,
    `phase` INT NOT NULL,
    `sms_type` INT,
    `user` VARCHAR(50) NOT NULL,
    `technician` VARCHAR(50),
    `date` DATE NOT NULL,
    `time` TIME NOT NULL,
    PRIMARY KEY(id),
    FOREIGN KEY(m_id) REFERENCES client_machines(m_id)
) ENGINE=INNODB;