// client_more_contacts_notifier.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:workshop_studio/providers/mysql_connection_provider.dart';
import 'package:workshop_studio/models/client/more_contacts_model.dart';
import 'package:workshop_studio/services/database_service.dart';

final selectedContactProvider = StateProvider<MoreContactsModel?>(
  (ref) => null,
);

class MoreContactsState {
  final MoreContactsModel? selectedContact;

  MoreContactsState({
    required this.contacts,
    required this.isLoading,
    this.selectedContact,
  });

  final List<MoreContactsModel> contacts;
  final bool isLoading;

  MoreContactsState copyWith({
    List<MoreContactsModel>? contacts,
    bool? isLoading,
    MoreContactsModel? selectedContact,
  }) {
    return MoreContactsState(
      contacts: contacts ?? this.contacts,
      isLoading: isLoading ?? this.isLoading,
      selectedContact: selectedContact ?? this.selectedContact,
    );
  }
}

final moreContactsProvider =
    StateNotifierProvider.family<MoreContactsNotifier, MoreContactsState, int>((
      ref,
      clientId,
    ) {
      final db = ref.read(mysqlServiceProvider);
      return MoreContactsNotifier(db, clientId: clientId);
    });

class MoreContactsNotifier extends StateNotifier<MoreContactsState> {
  final MySQLService _dbService;
  final int clientId;

  MoreContactsNotifier(this._dbService, {required this.clientId})
    : super(MoreContactsState(contacts: [], isLoading: false)) {
    loadContacts(clientId);
  }

  void selectContact(MoreContactsModel? contact) {
    state = state.copyWith(selectedContact: contact);
  }

  Future<void> loadContacts(int clientId) async {
    if (state.isLoading) return;

    state = state.copyWith(isLoading: true);

    final dataResult = await _dbService.query(
      'SELECT id, c_id, full_name, phone_n, job_title FROM more_contact WHERE c_id = :clientId ORDER BY id DESC',
      params: {'clientId': clientId},
    );

    final allContacts =
        dataResult.rows
            .map(
              (row) => MoreContactsModel.fromJson({
                'id': row.colByName('id')!,
                'c_id': row.colByName('c_id')!,
                'full_name': row.colByName('full_name')!,
                'phone_n': row.colByName('phone_n')!,
                'job_title': row.colByName('job_title') ?? '',
              }),
            )
            .toList();
    state = state.copyWith(contacts: allContacts, isLoading: false);
  }
}
