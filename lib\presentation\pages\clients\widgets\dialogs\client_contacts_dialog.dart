import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';
import 'package:workshop_studio/models/client/more_contacts_model.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/presentation/utils/upper_case_formatter.dart';
import 'package:workshop_studio/providers/clients/contacts/client_more_contacts_notifier.dart';
import 'package:workshop_studio/providers/clients/contacts/more_contacts_crud_provider.dart';

void showMoreContactsDialog(
  BuildContext context,
  WidgetRef ref,
  ClientModel selectedClient,
) {
  final contactNameController = TextEditingController();
  final contactJobController = TextEditingController();
  final contactPhoneController = TextEditingController();
  MoreContactsModel? selectedContact;

  final dialogWidth = MediaQuery.of(context).size.width / 2;
  final dialogHeight = MediaQuery.of(context).size.height / 1.5;

  showDialog(
    context: context,
    builder: (context) {
      final lang = AppLocalizations.of(context);

      return Consumer(
        builder: (context, ref, child) {
          final contactsState = ref.watch(
            moreContactsProvider(selectedClient.id),
          );
          final contacts = contactsState.contacts;
          final contactsCrud = ref.read(moreContactsCrudProvider);

          return ContentDialog(
            constraints: BoxConstraints.expand(
              width: dialogWidth,
              height: dialogHeight,
            ),
            title: const Text(
              'Client contacts',
              style: TextStyle(fontSize: 18),
            ),
            content: StatefulBuilder(
              builder: (context, setState) {
                Widget buildField(Widget field) {
                  return Expanded(
                    flex: 1,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 6),
                      child: field,
                    ),
                  );
                }

                return SizedBox(
                  height: dialogHeight - 100,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(bottom: 18),
                        child: Text(
                          selectedClient.cName,
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: FluentTheme.of(context).accentColor.lightest,
                          ),
                        ),
                      ),
                      CommandBarCard(
                        child: CommandBar(
                          overflowBehavior: CommandBarOverflowBehavior.wrap,
                          primaryItems: [
                            CommandBarButton(
                              icon: Icon(
                                selectedContact == null
                                    ? FluentIcons.add
                                    : FluentIcons.save,
                              ),
                              label: Text(
                                selectedContact == null ? lang.add : lang.edit,
                              ),
                              tooltip:
                                  selectedContact == null
                                      ? lang.add_client_tooltip
                                      : lang.edit_client_tooltip,
                              onPressed: () async {
                                if (contactNameController.text.isEmpty ||
                                    contactPhoneController.toString().isEmpty) {
                                  await showDialog(
                                    context: context,
                                    builder:
                                        (context) => ContentDialog(
                                          title: Text("Error"),
                                          content: Text(
                                            'Please fill in all required fields',
                                          ),
                                          actions: [
                                            Button(
                                              child: Text("Ok"),
                                              onPressed:
                                                  () => Navigator.pop(context),
                                            ),
                                          ],
                                        ),
                                  );
                                  return;
                                }

                                if (selectedContact == null) {
                                  final newContact = MoreContactsModel(
                                    id: 0,
                                    cId: selectedClient.id,
                                    fullName: contactNameController.text,
                                    phone: contactPhoneController.text,
                                    jobTitle: contactJobController.text,
                                  );
                                  await contactsCrud.createContact(newContact);
                                } else {
                                  final updatedContact = selectedContact!
                                      .copyWith(
                                        fullName: contactNameController.text,
                                        phone: contactPhoneController.text,
                                        jobTitle: contactJobController.text,
                                      );
                                  await contactsCrud.updateContact(
                                    updatedContact,
                                  );
                                }

                                ref.invalidate(
                                  moreContactsProvider(selectedClient.id),
                                );

                                setState(() {
                                  selectedContact = null;
                                  contactNameController.clear();
                                  contactJobController.clear();
                                  contactPhoneController.clear();
                                });
                              },
                            ),
                            if (selectedContact != null)
                              CommandBarButton(
                                icon: const Icon(FluentIcons.cancel),
                                label: const Text('Cancel'),
                                onPressed: () {
                                  setState(() {
                                    selectedContact = null;
                                    contactNameController.clear();
                                    contactJobController.clear();
                                    contactPhoneController.clear();
                                  });
                                },
                              ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 10),
                      Row(
                        children: [
                          buildField(
                            TextBox(
                              minLines: 1,
                              maxLines: 1,
                              expands: false,
                              padding: const EdgeInsets.all(10),
                              controller: contactNameController,
                              placeholder: 'Full Name',
                              inputFormatters: [UpperCaseTextFormatter()],
                            ),
                          ),
                          const SizedBox(width: 10),
                          buildField(
                            TextBox(
                              minLines: 1,
                              maxLines: 1,
                              expands: false,
                              padding: const EdgeInsets.all(10),
                              controller: contactJobController,
                              placeholder: 'Job Title',
                              inputFormatters: [UpperCaseTextFormatter()],
                            ),
                          ),
                          const SizedBox(width: 10),
                          buildField(
                            TextBox(
                              style: TextStyle(
                                letterSpacing: 1.2,
                                fontWeight: FontWeight.w500,
                              ),
                              keyboardType: TextInputType.phone,
                              minLines: 1,
                              maxLines: 1,
                              expands: false,
                              padding: const EdgeInsets.all(10),
                              controller: contactPhoneController,
                              placeholder: 'Phone Number',
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 10),
                      Expanded(
                        child: CardHighlight(
                          child: Container(
                            decoration: BoxDecoration(
                              color: FluentTheme.of(context).cardColor,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: ListView.separated(
                              itemCount: contacts.length,
                              separatorBuilder: (_, _) => const Divider(),
                              itemBuilder: (context, index) {
                                final contact = contacts[index];
                                final isSelected =
                                    selectedContact?.id == contact.id;

                                return ListTile.selectable(
                                  selected: isSelected,

                                  leading: CircleAvatar(
                                    backgroundColor: Colors.blue.lightest,
                                    child: Text(
                                      contact.fullName.isNotEmpty
                                          ? contact.fullName[0].toUpperCase()
                                          : '?',
                                    ),
                                  ),
                                  title: Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          contact.fullName,
                                          style: TextStyle(fontSize: 16),
                                        ),
                                      ),
                                      if (contact.jobTitle.isNotEmpty)
                                        Expanded(
                                          child: Text(
                                            contact.jobTitle,
                                            style: TextStyle(fontSize: 16),
                                          ),
                                        ),
                                      Expanded(
                                        child: Text(
                                          contact.phone,
                                          style: TextStyle(fontSize: 16),
                                        ),
                                      ),
                                    ],
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(FluentIcons.edit),
                                        onPressed: () {
                                          setState(() {
                                            selectedContact = contact;
                                            contactNameController.text =
                                                contact.fullName;
                                            contactJobController.text =
                                                contact.jobTitle;
                                            contactPhoneController.text =
                                                contact.phone;
                                          });
                                        },
                                      ),
                                      IconButton(
                                        icon: const Icon(FluentIcons.delete),
                                        onPressed: () async {
                                          final shouldDelete =
                                              await showDialog<bool>(
                                                context: context,
                                                builder:
                                                    (context) => ContentDialog(
                                                      title: Text(
                                                        'Confirm Deletion',
                                                      ),
                                                      content: Text(
                                                        'Are you sure you want to delete this contact?',
                                                      ),
                                                      actions: [
                                                        Button(
                                                          child: Text('Cancel'),
                                                          onPressed:
                                                              () =>
                                                                  Navigator.pop(
                                                                    context,
                                                                    false,
                                                                  ),
                                                        ),
                                                        FilledButton(
                                                          child: Text('Delete'),
                                                          onPressed:
                                                              () =>
                                                                  Navigator.pop(
                                                                    context,
                                                                    true,
                                                                  ),
                                                        ),
                                                      ],
                                                    ),
                                              ) ??
                                              false;

                                          if (shouldDelete) {
                                            await contactsCrud.deleteContact(
                                              contact.id,
                                            );
                                            ref.invalidate(
                                              moreContactsProvider(
                                                selectedClient.id,
                                              ),
                                            );
                                            setState(() {
                                              selectedContact = null;
                                              contactNameController.clear();
                                              contactJobController.clear();
                                              contactPhoneController.clear();
                                            });
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            actions: [
              Button(
                child: const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Text('Exit'),
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ],
          );
        },
      );
    },
  );
}
