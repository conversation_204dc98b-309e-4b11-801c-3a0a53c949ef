import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
// import 'package:workshop_studio/models/client/client_sold_model.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datagrid/transaction_history_devices_datagrid.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datagrid/transactions_history_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

void showClientSituationDialog(BuildContext context, var selectedTransaction) {
  final dialogWidth = MediaQuery.of(context).size.width / 1;
  final dialogHeight = MediaQuery.of(context).size.height / 1;
  final lang = AppLocalizations.of(context);
  final noteController = TextEditingController();

  List<String> paymentChoices = [
    "Cash",
    "Cheques",
    "Bank card",
    "Bank transfer",
    "Long term",
  ];
  String paymentChoice = paymentChoices[0];
  showDialog(
    context: context,
    builder:
        (context) => StatefulBuilder(
          builder: (context, setState) {
            Widget buildField(String label, Widget field) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 6),
                child: Row(
                  children: [
                    Expanded(flex: 1, child: Text(label)),
                    Expanded(flex: 2, child: field),
                  ],
                ),
              );
            }

            return ContentDialog(
              constraints: BoxConstraints.expand(
                width: dialogWidth,
                height: dialogHeight,
              ),
              title: Text("Client situation", style: TextStyle(fontSize: 18)),
              content: ScaffoldPage.withPadding(
                content: Row(
                  spacing: 8,
                  children: [
                    Flexible(
                      flex: 1,
                      child: CardHighlight(
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            buildField(
                              'Date',
                              Text("${DateTime.now()}".substring(0, 11)),
                            ),
                            SizedBox(height: 10),
                            buildField(
                              "Client name",
                              Text(
                                "HAMZA HALFI",
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  color:
                                      FluentTheme.of(
                                        context,
                                      ).accentColor.lightest,
                                ),
                              ),
                            ),
                            SizedBox(height: 10),
                            buildField(
                              'Payment choice',
                              ComboBox<String>(
                                isExpanded: true,
                                value: paymentChoice,
                                items: List.generate(
                                  paymentChoices.length,
                                  (index) => ComboBoxItem(
                                    value: paymentChoices[index],
                                    child: Text(paymentChoices[index]),
                                  ),
                                ),
                                onChanged:
                                    (value) =>
                                        setState(() => paymentChoice = value!),
                              ),
                            ),
                            buildField(
                              'Net payable',
                              NumberBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                mode: SpinButtonPlacementMode.none,
                                value: null,
                                placeholder: '0.00',
                                onChanged: (double? value) {},
                              ),
                            ),
                            Divider(
                              style: DividerThemeData(
                                thickness: 2,
                                horizontalMargin: EdgeInsets.symmetric(
                                  vertical: 10,
                                ),
                              ),
                            ),
                            buildField(
                              'TVA Value',
                              NumberBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                mode: SpinButtonPlacementMode.none,
                                value: null,
                                placeholder: '0',
                                onChanged: (double? value) {},
                              ),
                            ),
                            buildField(
                              'Total TVA',
                              NumberBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                mode: SpinButtonPlacementMode.none,
                                value: null,
                                placeholder: '0.00',
                                onChanged: (double? value) {},
                              ),
                            ),
                            buildField(
                              'Total',
                              NumberBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                mode: SpinButtonPlacementMode.none,
                                value: null,
                                placeholder: '0.00',
                                onChanged: (double? value) {},
                              ),
                            ),
                            buildField(
                              'Old balance',
                              NumberBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                mode: SpinButtonPlacementMode.none,
                                value: null,
                                placeholder: '0.00',
                                onChanged: (double? value) {},
                              ),
                            ),
                            buildField(
                              'New balance',
                              NumberBox(
                                style: TextStyle(
                                  letterSpacing: 1.2,
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                                mode: SpinButtonPlacementMode.none,
                                value: null,
                                placeholder: '0.00',
                                onChanged: (double? value) {},
                              ),
                            ),
                            Divider(
                              style: DividerThemeData(
                                thickness: 2,
                                horizontalMargin: EdgeInsets.symmetric(
                                  vertical: 10,
                                ),
                              ),
                            ),
                            buildField(
                              'End of warranty',
                              DatePicker(selected: null),
                            ),
                            buildField(
                              'Transaction note',
                              SizedBox(
                                height: 80,
                                child: TextBox(
                                  maxLines: null,
                                  padding: EdgeInsets.all(10),
                                  controller: noteController,
                                  placeholder: 'Enter note !',
                                ),
                              ),
                            ),
                            SizedBox(height: 20),
                            Row(
                              children: [
                                FilledButton(
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      spacing: 10,
                                      children: [
                                        Icon(FluentIcons.accept_medium),
                                        const Text('Validate'),
                                      ],
                                    ),
                                  ),
                                  onPressed: () => debugPrint('pressed button'),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Flexible(
                      flex: 3,
                      child: Column(
                        children: [
                          CommandBarCard(
                            child: CommandBar(
                              primaryItems: [
                                CommandBarButton(
                                  icon: const Icon(FluentIcons.edit),
                                  label: Text(lang.edit),
                                  tooltip: 'lang.edit_client_tooltip',
                                  onPressed:
                                      () => showClientSituationDialog(
                                        context,
                                        selectedTransaction,
                                      ),
                                ),
                                CommandBarButton(
                                  icon: const Icon(FluentIcons.delete),
                                  label: Text(lang.cancel),
                                  tooltip: 'lang.delete_client_tooltip',
                                  onPressed: () {},
                                ),
                                CommandBarSeparator(color: Colors.grey[50]),
                                CommandBarButton(
                                  icon: const Icon(FluentIcons.print),
                                  label: Text("payment bill"),
                                  tooltip: 'lang.delete_client_tooltip',
                                  onPressed: () {},
                                ),
                                CommandBarButton(
                                  icon: const Icon(FluentIcons.print),
                                  label: Text("Dicharge voucher"),
                                  tooltip: 'lang.delete_client_tooltip',
                                  onPressed: () {},
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 10),
                          Expanded(
                            flex: 2,
                            child: TransactionsHistoryDatagrid(),
                          ),
                          Expanded(
                            flex: 1,
                            child: TransactionHistoryDevicesDatagrid(),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                FilledButton(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: const Text('Exit'),
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            );
          },
        ),
  );
}
