// more_contacts_crud_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';
import 'package:workshop_studio/models/client/more_contacts_model.dart';
import 'package:workshop_studio/services/database_service.dart';

final moreContactsCrudProvider = Provider<MoreContactsCRUD>((ref) {
  final dbService = ref.read(mysqlServiceProvider);
  return MoreContactsCRUD(dbService);
});

class MoreContactsCRUD {
  final MySQLService _dbService;

  MoreContactsCRUD(this._dbService);

  Future<int> createContact(MoreContactsModel contact) async {
    final result = await _dbService.query(
      '''
      INSERT INTO more_contact (
        c_id, full_name, phone_n, job_title
      ) VALUES (
        :c_id, :full_name, :phone, :job_title
      )
    ''',
      params: {
        'c_id': contact.cId,
        'full_name': contact.fullName,
        'phone': contact.phone,
        'job_title': contact.jobTitle,
      },
    );
    return result.lastInsertID.toInt();
  }

  Future<bool> updateContact(MoreContactsModel contact) async {
    final result = await _dbService.query(
      '''
      UPDATE more_contact
      SET
        c_id = :c_id,
        full_name = :full_name,
        phone_n = :phone,
        job_title = :job_title
      WHERE id = :id
    ''',
      params: {
        'id': contact.id,
        'c_id': contact.cId,
        'full_name': contact.fullName,
        'phone': contact.phone,
        'job_title': contact.jobTitle,
      },
    );
    return result.affectedRows.toInt() > 0;
  }

  Future<bool> deleteContact(int contactId) async {
    final result = await _dbService.query(
      'DELETE FROM more_contact WHERE id = :id',
      params: {'id': contactId},
    );
    return result.affectedRows.toInt() > 0;
  }
}
