import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datasources/transaction_devices_data_source.dart';
import 'package:workshop_studio/providers/clients/transactions/client_transactions_notifier.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class TransactionHistoryDevicesDatagrid extends ConsumerWidget {
  const TransactionHistoryDevicesDatagrid({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final client = ref.watch(selectedClientProvider);
    if (client == null) {
      return const Center(child: Text('No client selected'));
    }
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    final state = ref.watch(clientTransactionsProvider(client));
    final dataSource = DevicesDataSource(context, devices: state.devices);
    return SfDataGridTheme(
      data: SfDataGridThemeData(
        headerColor: Colors.blue.light,
        selectionColor: Colors.blue.lightest,
        gridLineColor: Colors.transparent,
      ),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            source: dataSource,
            columnWidthMode: ColumnWidthMode.fill,
            selectionMode: SelectionMode.none,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'Type',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Type', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Brand',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Brand', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Serie',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Serie', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Model',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Model', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Serial',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Serial Number', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Issue',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Issue', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Price',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Price', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Services',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Services', style: headerTextStyle),
                ),
              ),
            ],
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
          ),
        ),
      ),
    );
  }
}
