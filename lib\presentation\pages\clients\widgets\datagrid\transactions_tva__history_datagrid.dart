import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datasources/transations_tva_data_source.dart';
import 'package:workshop_studio/providers/clients/transactions/client_transactions_notifier.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class TransactionsTvaHistoryDatagrid extends ConsumerWidget {
  const TransactionsTvaHistoryDatagrid({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final client = ref.watch(selectedClientProvider);
    if (client == null) {
      return const Center(child: Text('No client selected'));
    }
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    final state = ref.watch(clientTransactionsProvider(client));
    final dataSource = TransactionsTvaDataSource(
      context,
      transactionsTva: state.transactionsTva,
    );

    void handleRowSelection(DataGridRow row) {
      final transactionTva = state.transactionsTva.firstWhere(
        (c) => c.id == row.getCells()[0].value,
      );
      ref.read(selectedTransactionTvaProvider.notifier).state = transactionTva;
    }

    return SfDataGridTheme(
      data: SfDataGridThemeData(
        headerColor: Colors.blue.light,
        selectionColor: Colors.blue.lightest,
        gridLineColor: Colors.transparent,
      ),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: dataSource,
            columnWidthMode: ColumnWidthMode.fill,
            selectionMode: SelectionMode.single,
            onSelectionChanged:
                (addedRows, removedRows) => handleRowSelection(addedRows.first),
            columns: <GridColumn>[
              GridColumn(
                columnName: 'ID',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('T/N°', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'THT',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Total H.T', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'TVA',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('TVA', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'TOTAL TVA',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Total TVA', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'NET PAYABLE',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Net Payable', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'TYPE',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Type', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'DATE',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Date', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'TIME',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Time', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'WARRANTY',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('End of Warranty', style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'Note',
                label: Container(
                  alignment: Alignment.center,
                  child: Text('Note', style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
