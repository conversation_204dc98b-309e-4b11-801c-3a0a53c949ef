import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';

class CashRegisterTimeline extends StatelessWidget {
  const CashRegisterTimeline({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    DateTime? selected;
    final TextStyle textStyle = TextStyle(fontSize: 13);
    return Flexible(
      flex: 1,
      child: CardHighlight(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            spacing: 18,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                spacing: 8,
                children: [
                  Icon(FluentIcons.date_time2, size: 22, color: Colors.blue),
                  Text(lang.timeline),
                ],
              ),
              Divider(),
              RadioButton(
                checked: false,
                onChanged: (_) {},
                content: Text(lang.all, style: textStyle),
              ),
              RadioButton(
                checked: true,
                onChanged: (_) {},
                content: Text(lang.current_year, style: textStyle),
              ),
              RadioButton(
                checked: false,
                onChanged: (_) {},
                content: Text(lang.current_month, style: textStyle),
              ),
              RadioButton(
                checked: false,
                onChanged: (_) {},
                content: Text(lang.current_week, style: textStyle),
              ),
              Divider(),
              RadioButton(
                checked: false,
                onChanged: (_) {},
                content: Text(lang.timeline_picker, style: textStyle),
              ),
              DatePicker(
                header: lang.from,
                selected: selected,
                fieldFlex: const [2, 3, 2], // Same order as fieldOrder
                onChanged: (_) {}, // (time) => setState(() => selected = time),
              ),
              DatePicker(
                header: lang.to,
                selected: selected,
                fieldFlex: const [2, 3, 2], // Same order as fieldOrder
                onChanged: (_) {}, // (time) => setState(() => selected = time),
              ),
              Button(child: Text(lang.select), onPressed: () {}),
            ],
          ),
        ),
      ),
    );
  }
}
