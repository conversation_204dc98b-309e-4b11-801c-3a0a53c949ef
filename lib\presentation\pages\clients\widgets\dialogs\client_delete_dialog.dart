// Add this utility function
import 'package:fluent_ui/fluent_ui.dart';

Future<bool> showDeleteConfirmationDialog(BuildContext context) async {
  return await showDialog<bool>(
        context: context,
        builder:
            (context) => ContentDialog(
              title: const Text('Confirm Delete'),
              content: Column(
                spacing: 10,
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('Are you sure you want to delete this client?'),
                  Text(
                    'After deleting this client, you will lose all their bills',
                    style: TextStyle(color: Colors.red.light),
                  ),
                ],
              ),
              actions: [
                Button(
                  child: const Text('Cancel'),
                  onPressed: () => Navigator.pop(context, false),
                ),
                FilledButton(
                  child: const Text('Delete'),
                  onPressed: () => Navigator.pop(context, true),
                ),
              ],
            ),
      ) ??
      false;
}
