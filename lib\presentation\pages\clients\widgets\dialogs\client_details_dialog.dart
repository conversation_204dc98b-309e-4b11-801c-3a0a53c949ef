import 'package:fluent_ui/fluent_ui.dart';
import 'package:clipboard/clipboard.dart';
import 'package:workshop_studio/models/client/client_details_model.dart';

void showClientDetailsDialog(BuildContext context, ClientDetailsModel client) {
  final dialogWidth = MediaQuery.of(context).size.width / 2.5;
  final dialogHeight = MediaQuery.of(context).size.height / 1.5;

  showDialog(
    context: context,
    builder:
        (context) => ContentDialog(
          constraints: BoxConstraints.expand(
            width: dialogWidth,
            height: dialogHeight,
          ),
          title: const Text('Client Details', style: TextStyle(fontSize: 18)),
          content: ScaffoldPage.scrollable(
            children: [
              _buildDetailRow(
                context,
                FluentIcons.map_pin,
                'City:',
                client.city,
              ),
              Divider(style: DividerThemeData(thickness: 0.8)),
              _buildDetailRow(
                context,
                FluentIcons.location,
                'Address:',
                client.address,
              ),
              Divider(style: DividerThemeData(thickness: 0.8)),
              _buildDetailRow(
                context,
                FluentIcons.mail,
                'Email:',
                client.email,
              ),
              Divider(style: DividerThemeData(thickness: 0.8)),
              _buildDetailRow(
                context,
                FluentIcons.document_management,
                'RC:',
                client.rc,
              ),
              Divider(style: DividerThemeData(thickness: 0.8)),
              _buildDetailRow(
                context,
                FluentIcons.text_document,
                'NIF:',
                client.nif,
              ),
              Divider(style: DividerThemeData(thickness: 0.8)),
              _buildDetailRow(
                context,
                FluentIcons.certificate,
                'AI:',
                client.ai,
              ),
              Divider(style: DividerThemeData(thickness: 0.8)),
              _buildDetailRow(
                context,
                FluentIcons.report_document,
                'NIS:',
                client.nis,
              ),
              Divider(style: DividerThemeData(thickness: 0.8)),
              _buildTvaRow(context, client.tva),
            ],
          ),
          actions: [
            FilledButton(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: const Text('OK'),
              ),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        ),
  );
}

Widget _buildDetailRow(
  BuildContext context,
  IconData icon,
  String label,
  String value,
) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 12),
    child: Row(
      children: [
        Icon(icon, size: 18, color: FluentTheme.of(context).accentColor),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            overflow: TextOverflow.ellipsis,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        IconButton(
          icon: const Icon(FluentIcons.copy, size: 16),
          onPressed: () => FlutterClipboard.copy(value),
        ),
      ],
    ),
  );
}

Widget _buildTvaRow(BuildContext context, bool tvaStatus) {
  return Padding(
    padding: const EdgeInsets.symmetric(vertical: 12),
    child: Row(
      children: [
        Icon(
          FluentIcons.checkbox_composite,
          size: 18,
          color: FluentTheme.of(context).accentColor,
        ),
        const SizedBox(width: 12),
        const Expanded(
          flex: 2,
          child: Text(
            'TVA Subject:',
            style: TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
          ),
        ),
        tvaStatus
            ? Icon(FluentIcons.accept, color: Colors.successPrimaryColor)
            : Icon(FluentIcons.cancel, color: Colors.errorPrimaryColor),
      ],
    ),
  );
}
