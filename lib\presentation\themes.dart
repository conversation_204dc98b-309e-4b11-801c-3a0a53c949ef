import 'package:fluent_ui/fluent_ui.dart';

final lightTheme = FluentThemeData(
  accentColor: Colors.blue,
  brightness: Brightness.light,
  fontFamily: 'Roboto',
  buttonTheme: ButtonThemeData(
    filledButtonStyle: ButtonStyle(
      textStyle: WidgetStatePropertyAll(TextStyle(fontSize: 13)),
    ),
  ),
  typography: Typography.raw(
    caption: TextStyle(
      fontSize: 12.0,
      fontWeight: FontWeight.normal,
      color: Colors.black,
    ),
    body: TextStyle(
      fontSize: 14.0,
      fontWeight: FontWeight.normal,
      color: Colors.black,
    ),
    bodyLarge: TextStyle(
      fontSize: 15.0,
      fontWeight: FontWeight.normal,
      color: Colors.black,
    ),
    bodyStrong: TextStyle(
      fontSize: 14.0,
      fontWeight: FontWeight.w600,
      color: Colors.black,
    ),
    subtitle: TextStyle(
      fontSize: 20.0,
      fontWeight: FontWeight.w600,
      color: Colors.black,
    ),
    title: TextStyle(
      fontSize: 28.0,
      fontWeight: FontWeight.w600,
      color: Colors.black,
    ),
  ),
);
final darkTheme = FluentThemeData(
  accentColor: Colors.blue,
  brightness: Brightness.dark,
  buttonTheme: ButtonThemeData(
    filledButtonStyle: ButtonStyle(
      textStyle: WidgetStatePropertyAll(TextStyle(fontSize: 13)),
    ),
  ),
  fontFamily: 'Roboto',
  typography: const Typography.raw(
    caption: TextStyle(
      fontSize: 12.0,
      fontWeight: FontWeight.normal,
      color: Colors.white,
    ),
    body: TextStyle(
      fontSize: 14.0,
      fontWeight: FontWeight.normal,
      color: Colors.white,
    ),
    bodyLarge: TextStyle(
      fontSize: 15.0,
      fontWeight: FontWeight.normal,
      color: Colors.white,
    ),
    bodyStrong: TextStyle(
      fontSize: 14.0,
      fontWeight: FontWeight.w600,
      color: Colors.white,
    ),
    subtitle: TextStyle(
      fontSize: 20.0,
      fontWeight: FontWeight.w600,
      color: Colors.white,
    ),
    title: TextStyle(
      fontSize: 28.0,
      fontWeight: FontWeight.w600,
      color: Colors.white,
    ),
  ),
);
