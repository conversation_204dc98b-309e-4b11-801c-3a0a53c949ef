import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/transactions/widgets/transactions_datagrid.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/transactions/widgets/transactions_devices_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class TransactionsPage extends StatelessWidget {
  const TransactionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: 18,
      children: [
        CardHighlight(
          child: Row(
            spacing: 18,
            children: [
              Expanded(
                flex: 1,
                child: TextBox(
                  expands: false,
                  placeholder: "Transaction N°, Client, Date, SN°",
                  prefix: Padding(
                    padding: EdgeInsets.all(8),
                    child: Icon(FluentIcons.profile_search),
                  ),
                ),
              ),
              ComboBox<String>(
                value: "non",
                items: [
                  ComboBoxItem(value: 'non', child: Text(lang.no_filter)),
                  ComboBoxItem(value: 'cash', child: Text("Cash")),
                  ComboBoxItem(value: 'cheques', child: Text("Cheques")),
                  ComboBoxItem(value: 'bank_card', child: Text("Bank card")),
                  ComboBoxItem(
                    value: 'bank_transfer',
                    child: Text("Bank transfer"),
                  ),
                  ComboBoxItem(value: 'long_term', child: Text("Long term")),
                ],
                onChanged:
                    (
                      _,
                    ) {}, // disabled ? null : (color) => setState(() => selectedColor = color),
              ),
              Expanded(flex: 3, child: SizedBox()),
            ],
          ),
        ),
        CommandBar(
          overflowBehavior: CommandBarOverflowBehavior.wrap,
          primaryItems: [
            CommandBarButton(
              icon: const Icon(FluentIcons.edit),
              label: Text(lang.edit),
              tooltip: "Edit selected transation",
              onPressed: () {
                // Create something new!
              },
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.delete),
              label: Text(lang.delete),
              tooltip: "Delect selected transaction",
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(FluentIcons.print),
              label: Text("print"),
              tooltip: "Print transaction",
              onPressed: () {},
            ),
          ],
        ),
        Expanded(flex: 1, child: CardHighlight(child: TransactionsDatagrid())),
        CardHighlight(child: Text("Transaction devices")),
        Expanded(
          flex: 1,
          child: CardHighlight(child: TransactionsDevicesDatagrid()),
        ),
      ],
    );
  }
}
