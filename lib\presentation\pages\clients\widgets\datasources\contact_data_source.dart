import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/models/client/more_contacts_model.dart';

class ContactDataSource extends DataGridSource {
  List<DataGridRow> _contacts = [];

  ContactDataSource(List<MoreContactsModel> contacts) {
    _contacts =
        contacts
            .map<DataGridRow>(
              (contact) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'id', value: contact.id),
                  DataGridCell<String>(
                    columnName: 'fullName',
                    value: contact.fullName,
                  ),
                  DataGridCell<String>(
                    columnName: 'jobTitle',
                    value: contact.jobTitle,
                  ),
                  DataGridCell<String>(
                    columnName: 'phone',
                    value: contact.phone,
                  ),
                ],
              ),
            )
            .toList();
  }

  @override
  List<DataGridRow> get rows => _contacts;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map((dataCell) {
            return Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(8.0),
              child: Text(dataCell.value.toString()),
            );
          }).toList(),
    );
  }
}
