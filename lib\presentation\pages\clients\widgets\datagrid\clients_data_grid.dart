import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datasources/data_source.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class ClientsDataGrid extends ConsumerStatefulWidget {
  const ClientsDataGrid({super.key});

  @override
  ConsumerState<ClientsDataGrid> createState() => _ClientsDataGridState();
}

class _ClientsDataGridState extends ConsumerState<ClientsDataGrid> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    final lang = AppLocalizations.of(context);
    final clientState = ref.watch(clientProvider);
    final dataSource = ClientDataSource(context, clients: clientState.clients);
    final notifier = ref.read(clientProvider.notifier);

    void handleRowSelection(DataGridRow row) {
      final client = clientState.clients.firstWhere(
        (c) => c.id == row.getCells()[0].value,
      );
      ref.read(selectedClientProvider.notifier).state = client;
    }

    FocusNode dataGridFocusNode = FocusNode();

    void handleArrowKeyNavigation(KeyEvent event) {
      if (clientState.clients.isEmpty) return;
      if (event is! KeyDownEvent) return;
      final selected = ref.read(selectedClientProvider);
      int currentIndex =
          selected == null
              ? -1
              : clientState.clients.indexWhere((c) => c.id == selected.id);

      if (event.logicalKey == LogicalKeyboardKey.arrowDown) {
        int nextIndex = (currentIndex + 1).clamp(
          0,
          clientState.clients.length - 1,
        );
        if (nextIndex != currentIndex &&
            nextIndex < clientState.clients.length) {
          ref.read(selectedClientProvider.notifier).state =
              clientState.clients[nextIndex];
        }
      } else if (event.logicalKey == LogicalKeyboardKey.arrowUp) {
        int prevIndex = (currentIndex - 1).clamp(
          0,
          clientState.clients.length - 1,
        );
        if (prevIndex != currentIndex && prevIndex >= 0) {
          ref.read(selectedClientProvider.notifier).state =
              clientState.clients[prevIndex];
        }
      }
    }

    if (clientState.isLoading) {
      return Center(child: ProgressRing());
    }

    // Show "no data yet" if there are no devices
    if (clientState.clients.isEmpty) {
      return Center(
        child: Text(
          'NO DATA',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: const Color.fromARGB(255, 97, 96, 96),
          ),
        ),
      );
    }

    return Focus(
      focusNode: dataGridFocusNode,
      autofocus: true,
      onKeyEvent: (FocusNode node, KeyEvent event) {
        handleArrowKeyNavigation(event);
        return KeyEventResult.handled;
      },
      child: Column(
        children: [
          Expanded(
            child: ClipRect(
              clipper: DataGridClipper(),
              child: SfDataGridTheme(
                data: SfDataGridThemeData(
                  headerColor: Colors.blue.light,
                  selectionColor: Colors.blue.lightest,
                  gridLineColor: Colors.transparent,
                ),
                child: ScrollConfiguration(
                  behavior: const ScrollBehavior().copyWith(scrollbars: false),
                  child: SfDataGrid(
                    headerGridLinesVisibility: GridLinesVisibility.none,
                    gridLinesVisibility: GridLinesVisibility.none,
                    source: dataSource,
                    columnWidthMode: ColumnWidthMode.fill,
                    columnResizeMode: ColumnResizeMode.onResize,
                    isScrollbarAlwaysShown: false,
                    selectionMode: SelectionMode.single,
                    onSelectionChanged: (addedRows, removedRows) {
                      if (addedRows.isNotEmpty) {
                        handleRowSelection(addedRows.first);
                      }
                    },
                    columns: <GridColumn>[
                      GridColumn(
                        columnName: 'ID',
                        label: Container(
                          padding: EdgeInsets.all(16.0),
                          alignment: Alignment.center,
                          child: Text(lang.id, style: headerTextStyle),
                        ),
                      ),
                      GridColumn(
                        columnName: 'Name',
                        label: Container(
                          padding: EdgeInsets.all(16.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.client.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'Phone 1',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.phone_n.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'Phone 2',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.phone_n.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'Type',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.type.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'Date',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.date.toUpperCase(),
                            style: headerTextStyle,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'Time',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.time.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'Debts',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.debts.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                      GridColumn(
                        columnName: 'User',
                        label: Container(
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.center,
                          child: Text(
                            lang.user.toUpperCase(),
                            style: headerTextStyle,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          SfDataPagerTheme(
            data: SfDataPagerThemeData(
              backgroundColor: FluentTheme.of(
                context,
              ).resources.controlStrokeColorSecondary.withAlpha(10),
            ),
            child: SfDataPager(
              delegate: ClientDataPagerDelegate(notifier, clientState),
              pageCount:
                  ClientDataPagerDelegate(
                    notifier,
                    clientState,
                  ).pageCount.toDouble(),
              direction: Axis.horizontal,
            ),
          ),
        ],
      ),
    );
  }
}

class ClientDataPagerDelegate extends DataPagerDelegate {
  final ClientNotifier notifier;
  final ClientState state;

  ClientDataPagerDelegate(this.notifier, this.state);

  @override
  Future<bool> handlePageChange(int oldPageIndex, int newPageIndex) async {
    // Only load if page actually changed
    if (newPageIndex != state.currentPage) {
      notifier.setPage(newPageIndex);
      return true; // Indicate successful page change
    }
    return false;
  }

  int get rowCount => state.totalCount;

  int get pageSize => 50;

  bool get shouldRecalculatePageCount => true;

  int get pageCount => (rowCount / pageSize).ceil();
}
