// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get language => 'Language';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get reception => 'Reception';

  @override
  String get clients => 'Clients';

  @override
  String get devices => 'Devices';

  @override
  String get device => 'Device';

  @override
  String get receiptList => 'Receipt List';

  @override
  String get cashFlow => 'Cash Flow';

  @override
  String get expenses => 'Expenses';

  @override
  String get products => 'Products';

  @override
  String get technicians => 'Technicians';

  @override
  String get settings => 'Settings';

  @override
  String get exitApp => 'Confirm close';

  @override
  String get exitAppMsg => 'Are you sure you want to close this window ?';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get new_add => 'New';

  @override
  String get add => 'Add';

  @override
  String get edit => 'Edit';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get select => 'Select';

  @override
  String get name => 'Name';

  @override
  String get type => 'Type';

  @override
  String get phone_n => 'Phone N°';

  @override
  String get device_type => 'Device type';

  @override
  String get brand => 'Brand';

  @override
  String get serie => 'Serie';

  @override
  String get model => 'Model';

  @override
  String get serial_n => 'Serial N°';

  @override
  String get issue => 'Issue(s)';

  @override
  String get deadline => 'Deadline';

  @override
  String get days => 'Day(s)';

  @override
  String get estimated_price => 'Estimated price';

  @override
  String get remarks => 'Remarks';

  @override
  String get warranty => 'Warranty';

  @override
  String get emergency => 'Emergency';

  @override
  String get print_receipt => 'Print receipt';

  @override
  String get cancel_reception => 'Cancel reception';

  @override
  String get close_after_printing => 'Close after printing';

  @override
  String get id => 'ID';

  @override
  String get generate => 'Barcode Generator';

  @override
  String get print_barcode_tooltip => 'Print Barcode';

  @override
  String get add_client_tooltip => 'Add new client';

  @override
  String get edit_client_tooltip => 'Edit selected client information';

  @override
  String get select_client_tooltip => 'Select from existing clients list';

  @override
  String get add_device_tooltip => 'Add new device to the list';

  @override
  String get edit_device_tooltip => 'Edit selected device informations';

  @override
  String get cancel_device_tooltip => 'Cancel editing';

  @override
  String get remove_device_tooltip => 'Remove device from the list';

  @override
  String get timeline => 'Timeline';

  @override
  String get all => 'All';

  @override
  String get current_year => 'Current year';

  @override
  String get current_month => 'Current month';

  @override
  String get current_week => 'Current week';

  @override
  String get timeline_picker => 'Timeline picker';

  @override
  String get search => 'Search';

  @override
  String get no_filter => 'Without filter';

  @override
  String get dealer => 'Dealer';

  @override
  String get particular => 'Particular';

  @override
  String get company => 'Company';

  @override
  String get new_reception => 'New reception';

  @override
  String get payment => 'Payment';

  @override
  String get transactions => 'transactions history';

  @override
  String get user => 'User';

  @override
  String get debts => 'Debts';

  @override
  String get time => 'Time';

  @override
  String get date => 'Date';

  @override
  String get from => 'From';

  @override
  String get to => 'To';

  @override
  String get new_reception_tooltip =>
      'Create new reception for selected client';

  @override
  String get delete_client_tooltip => 'Delete selected client';

  @override
  String get client_payment_tooltip => 'Add payment for selected client';

  @override
  String get transactions_tooltip =>
      'Manage selected client transactions history';

  @override
  String get phase => 'Phase';

  @override
  String get phase_tooltip => 'Manage device phase';

  @override
  String get technician => 'Technician';

  @override
  String get technician_tooltip => 'Technician who worked on this device';

  @override
  String get print_device => 'Print';

  @override
  String get print_device_tooltip => 'Print device owner receipt';

  @override
  String get situation => 'Situation';

  @override
  String get situation_tooltip => 'Manage device owner situation';

  @override
  String get delivery => 'Delivery';

  @override
  String get delivery_tooltip => 'Change device status to delivery stage';

  @override
  String get traceability => 'Traceability';

  @override
  String get traceability_tooltip => 'Selected device traceability';

  @override
  String get client => 'Client';

  @override
  String get on_hold => 'On hold';

  @override
  String get diagnostic => 'Diagnostic';

  @override
  String get confirmation => 'Confirmation';

  @override
  String get confirmed => 'Confirmed';

  @override
  String get repaired => 'Repaired';

  @override
  String get not_repaired => 'Not repaired';

  @override
  String get rejected => 'Rejected';

  @override
  String get parts_unavailable => 'Part unavailable';

  @override
  String get devices_at_workshop => 'Devices at workshop';

  @override
  String get delivered_devices => 'Delivered devices';

  @override
  String get cancel_delivery => 'Cancel delivery';

  @override
  String get cancel_delivery_tooltip => 'Cancel device delivery';

  @override
  String get edit_owner => 'Edit owner';

  @override
  String get edit_owner_tooltip => 'Change receipt owner (client)';

  @override
  String get delete_receipt => 'Delete receipt';

  @override
  String get delete_receipt_tooltip => 'Delete receipt permanently';

  @override
  String get receipt_n => 'Receipt N°';

  @override
  String get can_not => 'You can not do that :/';

  @override
  String get select_device => 'Please select a device first';

  @override
  String get non => 'non';

  @override
  String get device_managment => 'Device Phase Management';

  @override
  String get device_price => 'Device Price';

  @override
  String get est_to_final => 'From estimated price to final price';

  @override
  String get price => 'Price';

  @override
  String get set => 'Set';

  @override
  String get update => 'Update';

  @override
  String get loading => 'Loading';

  @override
  String get found => 'found';

  @override
  String get device_phases => 'Device Phases';

  @override
  String get change => 'Change';

  @override
  String get sms_text => 'SMS TEXT';

  @override
  String get auto_sms => 'Auto SMS message...';

  @override
  String get send_sms => 'Send SMS';

  @override
  String get services => 'Services';

  @override
  String get enter => 'Enter';

  @override
  String get and => 'And';

  @override
  String get save => 'Save';

  @override
  String get exit => 'Exit';

  @override
  String get final_price => 'Final price';

  @override
  String get priceUpdatedSuccessfully => 'Price updated successfully';

  @override
  String get technicianAssignedSuccessfully =>
      'Technician assigned successfully';

  @override
  String phaseChangedSuccessfully(String phase) {
    return 'Phase changed to \"$phase\" successfully';
  }

  @override
  String get remarksUpdatedSuccessfully => 'Remarks updated successfully';

  @override
  String get remarksSavedSuccessfully => 'Remarks saved successfully';

  @override
  String get failedToUpdatePrice => 'Failed to update price';

  @override
  String failedToUpdatePriceWithError(String error) {
    return 'Failed to update price: $error';
  }

  @override
  String get failedToAssignTechnician => 'Failed to assign technician';

  @override
  String failedToAssignTechnicianWithError(String error) {
    return 'Failed to assign technician: $error';
  }

  @override
  String get failedToChangePhase => 'Failed to change phase';

  @override
  String failedToChangePhaseWithError(String error) {
    return 'Failed to change phase: $error';
  }

  @override
  String get failedToSaveRemarks => 'Failed to save remarks';

  @override
  String failedToSendSms(String error) {
    return 'Failed to send SMS: $error';
  }

  @override
  String get sendSms => 'Send SMS';

  @override
  String get smsTemplateLoadedMessage =>
      'An SMS template has been loaded for this phase. Would you like to send the SMS notification to the client now?';

  @override
  String get smsMessage => 'SMS Message';

  @override
  String get smsError => 'SMS Error';

  @override
  String get error => 'Error';

  @override
  String technicianWillBe(String technician) {
    return 'Technician will be: \"$technician\"';
  }

  @override
  String currentTechnician(String technician) {
    return 'Current technician: $technician';
  }

  @override
  String phaseSuccessfullyChanged(String phase) {
    return 'Phase successfully changed to \"$phase\"';
  }

  @override
  String phaseWillChange(String oldPhase, String newPhase) {
    return 'Phase will change from \"$oldPhase\" to \"$newPhase\"';
  }

  @override
  String get ok => 'OK';

  @override
  String get unknown => 'Unknown';

  @override
  String get notAssigned => 'Not assigned';

  @override
  String get smsSentSuccessfully => 'SMS sent successfully';

  @override
  String get failedToSendSmsViaFrontline => 'Failed to send SMS via Frontline';

  @override
  String smsServiceTimeout(String message) {
    return 'SMS service timeout: $message';
  }

  @override
  String get frontlineSmsNotConfigured => 'Frontline SMS not configured';

  @override
  String get connectionTimedOut => 'Connection timed out';
}
