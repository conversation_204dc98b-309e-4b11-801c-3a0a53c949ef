import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/models/device/device_model.dart';

class DevicesDataSource extends DataGridSource {
  final BuildContext context;
  final List<DeviceModel> devices;
  late final List<DataGridRow> _rows;

  DevicesDataSource(this.context, {required this.devices}) {
    _rows =
        devices
            .map<DataGridRow>(
              (d) => DataGridRow(
                cells: [
                  DataGridCell<String>(
                    columnName: 'Type',
                    value: d.machineType,
                  ),
                  DataGridCell<String>(
                    columnName: 'Brand',
                    value: d.machineBrand,
                  ),
                  DataGridCell<String>(
                    columnName: 'Serie',
                    value: d.machineSerie,
                  ),
                  DataGridCell<String>(
                    columnName: 'Model',
                    value: d.machineModel ?? '',
                  ),
                  DataGridCell<String>(
                    columnName: 'Serial',
                    value: d.serialNumber ?? '',
                  ),
                  DataGridCell<String>(
                    columnName: 'Issue',
                    value: d.problem ?? '',
                  ),
                  DataGridCell<double>(columnName: 'Price', value: d.price),
                  DataGridCell<String>(
                    columnName: 'Services',
                    value: d.note ?? '',
                  ),
                ],
              ),
            )
            .toList();
  }

  @override
  List<DataGridRow> get rows => _rows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50); // Alternate row color
      }
      return Colors.transparent; // Default row color
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map((cell) {
            return Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(8.0),
              child: Text(
                textAlign: TextAlign.center,
                cell.value.toString().toUpperCase(),
              ),
            );
          }).toList(),
    );
  }
}
