import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/data_grid/devices_atworkshop_datagrid.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/dialogs/device_phase_dialog.dart';
import 'package:workshop_studio/presentation/pages/devices/widgets/dialogs/device_edit_dialog.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/providers/clients/search/search_query_notifier.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_notifier.dart';

class DevicesAtWorkshop extends ConsumerStatefulWidget {
  const DevicesAtWorkshop({super.key});

  @override
  ConsumerState<DevicesAtWorkshop> createState() => _DevicesAtWorkshopState();
}

class _DevicesAtWorkshopState extends ConsumerState<DevicesAtWorkshop> {
  TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final search = ref.watch(searchQueryProvider('devicesAtWorkshop'));
    final searchNotifier = ref.read(
      searchQueryProvider('devicesAtWorkshop').notifier,
    );
    final phaseFilter = ref.watch(phaseFilterProvider('devicesAtWorkshop'));
    final phaseFilterNotifier = ref.read(
      phaseFilterProvider('devicesAtWorkshop').notifier,
    );

    // Keep TextBox in sync with provider
    if (searchController.text != search) {
      searchController.text = search;
      searchController.selection = TextSelection.fromPosition(
        TextPosition(offset: search.length),
      );
    }

    return Column(
      spacing: 8,
      children: [
        CardHighlight(
          child: Row(
            spacing: 18,
            children: [
              Expanded(
                flex: 1,
                child: TextBox(
                  controller: searchController,
                  expands: false,
                  suffix: IconButton(
                    icon: const Icon(FluentIcons.clear),
                    onPressed: () {
                      searchNotifier.clear();
                      ref
                          .read(deviceProvider("devicesAtWorkshop").notifier)
                          .reset();
                    },
                  ),
                  placeholder: lang.search,
                  onChanged: (value) => searchNotifier.setQuery(value),
                  prefix: Padding(
                    padding: EdgeInsets.all(8),
                    child: Icon(CarbonIcons.search),
                  ),
                ),
              ),
              ComboBox<String>(
                value: phaseFilter?.toString() ?? "all",
                items: [
                  ComboBoxItem(value: "all", child: Text(lang.no_filter)),
                  ComboBoxItem(value: '0', child: Text(lang.on_hold)),
                  ComboBoxItem(value: '1', child: Text(lang.diagnostic)),
                  ComboBoxItem(value: '2', child: Text(lang.confirmation)),
                  ComboBoxItem(value: '3', child: Text(lang.confirmed)),
                  ComboBoxItem(value: '4', child: Text(lang.rejected)),
                  ComboBoxItem(value: '5', child: Text(lang.not_repaired)),
                  ComboBoxItem(value: '6', child: Text(lang.parts_unavailable)),
                  ComboBoxItem(value: '7', child: Text(lang.repaired)),
                ],
                onChanged: (value) {
                  if (value == "all") {
                    phaseFilterNotifier.state = null;
                  } else {
                    phaseFilterNotifier.state = int.tryParse(value!);
                  }
                  // Reset to first page and reload devices
                  ref
                      .read(deviceProvider("devicesAtWorkshop").notifier)
                      .reset();
                },
              ),
              Spacer(flex: 2),
              IconButton(
                icon: Icon(FluentIcons.refresh, size: 18.0),
                onPressed: () {
                  ref
                      .read(deviceProvider("devicesAtWorkshop").notifier)
                      .reset();
                },
              ),
            ],
          ),
        ),
        CommandBar(
          overflowBehavior: CommandBarOverflowBehavior.wrap,
          primaryItems: [
            CommandBarButton(
              icon: const Icon(CarbonIcons.queued, size: 22),
              label: Text(lang.phase),
              tooltip: lang.phase_tooltip,
              onPressed: () async {
                final selectedDevice = ref.read(selectedDeviceProvider);
                if (selectedDevice == null) {
                  await unselectedDeviceInfoBar(context, lang);
                  return;
                }
                showDevicePhaseDialog(context, ref);
              },
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.edit),
              label: Text(lang.edit),
              tooltip: lang.edit_device_tooltip,
              onPressed: () async {
                final selectedDevice = ref.read(selectedDeviceProvider);
                if (selectedDevice == null) {
                  await unselectedDeviceInfoBar(context, lang);
                  return;
                }
                showDeviceEditDialog(context, ref);
              },
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.delete),
              label: Text(lang.delete),
              tooltip: lang.remove_device_tooltip,
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(CarbonIcons.delivery, size: 22),
              label: Text(lang.delivery),
              tooltip: lang.delivery_tooltip,
              onPressed: () {},
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.full_history),
              label: Text(lang.traceability),
              tooltip: lang.traceability_tooltip,
              onPressed: () {},
            ),
            CommandBarButton(
              icon: const Icon(FluentIcons.payment_card),
              label: Text(lang.situation),
              tooltip: lang.situation_tooltip,
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(FluentIcons.user_event),
              label: Text(lang.technician),
              tooltip: lang.technician_tooltip,
              onPressed: () {},
            ),
            CommandBarSeparator(color: Colors.grey[50]),
            CommandBarButton(
              icon: const Icon(FluentIcons.print),
              label: Text(lang.print_device),
              tooltip: lang.print_device_tooltip,
              onPressed: () {},
            ),
          ],
        ),
        // Replace Flexible with Expanded to avoid ParentDataWidget conflict
        Expanded(flex: 1, child: DevicesAtworkshopDataGrid()),
      ],
    );
  }
}

Future<void> unselectedDeviceInfoBar(
  BuildContext context,
  AppLocalizations lang,
) {
  return displayInfoBar(
    context,
    builder: (context, close) {
      return InfoBar(
        title: Text(lang.can_not),
        content: Text(lang.select_device),
        action: IconButton(
          icon: const Icon(FluentIcons.clear),
          onPressed: close,
        ),
        severity: InfoBarSeverity.warning,
      );
    },
  );
}
