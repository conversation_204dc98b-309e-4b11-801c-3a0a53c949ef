import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/providers/clients/timeline_filter_provider.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';

class ClientsTimeline extends ConsumerWidget {
  const ClientsTimeline({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lang = AppLocalizations.of(context);
    final filter = ref.watch(timelineFilterProviderFamily('clients'));
    final notifier = ref.read(timelineFilterProviderFamily('clients').notifier);
    final TextStyle textStyle = const TextStyle(fontSize: 13);

    return Flexible(
      flex: 1,
      child: CardHighlight(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            spacing: 18,
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                runAlignment: WrapAlignment.center,
                alignment: WrapAlignment.center,
                runSpacing: 8,
                spacing: 8,
                children: [
                  Icon(FluentIcons.date_time2, size: 22, color: Colors.blue),
                  Padding(
                    padding: const EdgeInsets.only(top: 3.0),
                    child: Text(lang.timeline),
                  ),
                ],
              ),
              const Divider(),
              RadioButton(
                checked: filter.type == TimelineFilterType.all,
                onChanged: (_) => notifier.setAll(),
                content: Text(lang.all, style: textStyle),
              ),
              RadioButton(
                checked: filter.type == TimelineFilterType.currentYear,
                onChanged: (_) => notifier.setCurrentYear(),
                content: Text(lang.current_year, style: textStyle),
              ),
              RadioButton(
                checked: filter.type == TimelineFilterType.currentMonth,
                onChanged: (_) => notifier.setCurrentMonth(),
                content: Text(lang.current_month, style: textStyle),
              ),
              RadioButton(
                checked: filter.type == TimelineFilterType.currentWeek,
                onChanged: (_) => notifier.setCurrentWeek(),
                content: Text(lang.current_week, style: textStyle),
              ),
              const Divider(),
              RadioButton(
                checked: filter.type == TimelineFilterType.customRange,
                onChanged: (_) {
                  final now = DateTime.now();
                  final startOfYear = DateTime(now.year, 1, 1);
                  final today = DateTime(now.year, now.month, now.day);
                  notifier.setCustomRange(startOfYear, today);
                },
                content: Text(lang.timeline_picker, style: textStyle),
              ),
              DatePicker(
                header: lang.from,
                selected: filter.from,
                fieldFlex: const [2, 3, 2],
                onChanged: (date) => notifier.setFrom(date),
              ),
              DatePicker(
                header: lang.to,
                selected: filter.to,
                fieldFlex: const [2, 3, 2],
                onChanged: (date) => notifier.setTo(date),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
