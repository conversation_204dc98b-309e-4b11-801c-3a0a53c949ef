import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/technicians/technicians_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class TechniciansDatagrid extends StatefulWidget {
  const TechniciansDatagrid({super.key});

  @override
  State<TechniciansDatagrid> createState() => _TechniciansDatagridState();
}

class _TechniciansDatagridState extends State<TechniciansDatagrid> {
  List<TechniciansModel> employees = <TechniciansModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'id',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.id, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'first_name',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("FIRST NAME", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'last_name',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("LAST NAME", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'job_title',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("JOB TITLE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'joining_date',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("JOINING DATE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'salary',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("SALARY", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<TechniciansModel> getEmployeeData() {
    return [
      TechniciansModel(
        id: 1,
        firstName: "MEDDAHI",
        lastName: "FATEH",
        jobTitle: "Gérant",
        joiningDate: DateTime.now(),
        salary: 0.00,
      ),
      TechniciansModel(
        id: 1,
        firstName: "MEDDAHI",
        lastName: "FATEH",
        jobTitle: "Gérant",
        joiningDate: DateTime.now(),
        salary: 0.00,
      ),
      TechniciansModel(
        id: 1,
        firstName: "MEDDAHI",
        lastName: "FATEH",
        jobTitle: "Gérant",
        joiningDate: DateTime.now(),
        salary: 0.00,
      ),
      TechniciansModel(
        id: 1,
        firstName: "MEDDAHI",
        lastName: "FATEH",
        jobTitle: "Gérant",
        joiningDate: DateTime.now(),
        salary: 0.00,
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<TechniciansModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'id', value: e.id),
                  DataGridCell<String>(
                    columnName: 'first_name',
                    value: e.firstName,
                  ),
                  DataGridCell<String>(
                    columnName: 'last_name',
                    value: e.lastName,
                  ),
                  DataGridCell<String>(
                    columnName: 'job_title',
                    value: e.jobTitle,
                  ),
                  DataGridCell<DateTime>(
                    columnName: 'joining_date',
                    value: e.joiningDate,
                  ),
                  DataGridCell<double>(columnName: 'salary', value: e.salary),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
