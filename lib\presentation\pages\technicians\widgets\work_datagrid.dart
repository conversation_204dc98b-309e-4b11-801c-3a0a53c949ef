import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/technicians/work_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class WorkDatagrid extends StatefulWidget {
  const WorkDatagrid({super.key});

  @override
  State<WorkDatagrid> createState() => _WorkDatagridState();
}

class _WorkDatagridState extends State<WorkDatagrid> {
  List<WorkTableModel> employees = <WorkTableModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'date',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.date, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'client',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.client, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'device',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("DEVICE", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<WorkTableModel> getEmployeeData() {
    return [
      WorkTableModel(
        date: DateTime.now(),
        client: "HAMZA HALFI",
        device: "APPLE MACBOOK PRO 13",
      ),
      WorkTableModel(
        date: DateTime.now(),
        client: "HAMZA HALFI",
        device: "APPLE MACBOOK PRO 13",
      ),
      WorkTableModel(
        date: DateTime.now(),
        client: "HAMZA HALFI",
        device: "APPLE MACBOOK PRO 13",
      ),
      WorkTableModel(
        date: DateTime.now(),
        client: "HAMZA HALFI",
        device: "APPLE MACBOOK PRO 13",
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<WorkTableModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<DateTime>(columnName: 'date', value: e.date),
                  DataGridCell<String>(columnName: 'client', value: e.client),
                  DataGridCell<String>(columnName: 'device', value: e.device),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
