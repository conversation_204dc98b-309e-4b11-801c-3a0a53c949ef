class TechniciansModel {
  TechniciansModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.jobTitle,
    required this.joiningDate,
    required this.salary,
  });

  final int id;
  final String firstName;
  final String lastName;
  final String jobTitle;
  final DateTime joiningDate;
  final double salary;

  // Factory constructor to create instance from JSON
  factory TechniciansModel.fromJson(Map<String, dynamic> json) {
    return TechniciansModel(
      id: json['id'] as int,
      firstName: json['firstName'] as String,
      lastName: json['lastName'] as String,
      jobTitle: json['jobTitle'] as String,
      joiningDate: DateTime.parse(json['joiningDate'] as String),
      salary: (json['salary'] as num).toDouble(),
    );
  }

  // Method to convert instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'jobTitle': jobTitle,
      'joiningDate': joiningDate.toIso8601String(),
      'salary': salary,
    };
  }

  // Method to create a copy with updated fields
  TechniciansModel copyWith({
    int? id,
    String? firstName,
    String? lastName,
    String? jobTitle,
    DateTime? joiningDate,
    double? salary,
  }) {
    return TechniciansModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      jobTitle: jobTitle ?? this.jobTitle,
      joiningDate: joiningDate ?? this.joiningDate,
      salary: salary ?? this.salary,
    );
  }
}
