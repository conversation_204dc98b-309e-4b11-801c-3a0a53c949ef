import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/sms_reports/widgets/sms_reports_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class SmsReportsPage extends StatelessWidget {
  const SmsReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return Column(
      spacing: 18,
      children: [
        CardHighlight(
          child: Row(
            spacing: 18,
            children: [
              Expanded(
                flex: 1,
                child: TextBox(
                  expands: false,
                  placeholder: lang.search,
                  prefix: Padding(
                    padding: EdgeInsets.all(8),
                    child: Icon(CarbonIcons.search),
                  ),
                ),
              ),
              ComboBox<String>(
                value: "non",
                items: [
                  ComboBoxItem(value: 'non', child: Text(lang.no_filter)),
                  ComboBoxItem(
                    value: 'confirmation',
                    child: Text(lang.confirmation),
                  ),
                  ComboBoxItem(value: 'repaired', child: Text(lang.repaired)),
                  ComboBoxItem(
                    value: 'not_repaired',
                    child: Text(lang.not_repaired),
                  ),
                  ComboBoxItem(
                    value: 'parts_unavailable',
                    child: Text(lang.parts_unavailable),
                  ),
                ],
                onChanged:
                    (
                      _,
                    ) {}, // disabled ? null : (color) => setState(() => selectedColor = color),
              ),
              Expanded(flex: 3, child: SizedBox()),
            ],
          ),
        ),
        Expanded(child: CardHighlight(child: SmsReportsDatagrid())),
      ],
    );
  }
}
