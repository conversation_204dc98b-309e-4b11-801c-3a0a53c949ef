import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/technicians/widgets/technician_titmeline.dart';
import 'package:workshop_studio/presentation/pages/technicians/widgets/technicians_datagrid.dart';
import 'package:workshop_studio/presentation/pages/technicians/widgets/work_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class TechniciansPage extends StatelessWidget {
  const TechniciansPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return ScaffoldPage.withPadding(
      content: Row(
        spacing: 18,
        children: [
          Flexible(flex: 1, child: TechnicianTitmeline()),
          Flexible(
            flex: 6,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              spacing: 18,
              children: [
                CardHighlight(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CommandBar(
                          overflowBehavior: CommandBarOverflowBehavior.wrap,
                          primaryItems: [
                            CommandBarButton(
                              icon: const Icon(FluentIcons.add),
                              label: Text(lang.edit),
                              tooltip: "",
                              onPressed: () {
                                // Create something new!
                              },
                            ),
                            CommandBarButton(
                              icon: const Icon(FluentIcons.edit),
                              label: Text(lang.edit),
                              tooltip: "",
                              onPressed: () {},
                            ),
                            CommandBarButton(
                              icon: const Icon(FluentIcons.delete),
                              label: Text(lang.delete),
                              tooltip: "",
                              onPressed: () {},
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: Icon(FluentIcons.refresh, size: 18.0),
                        onPressed: () {},
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Row(
                    spacing: 18,
                    children: [
                      Expanded(
                        flex: 1,
                        child: CardHighlight(child: TechniciansDatagrid()),
                      ),
                      Expanded(
                        flex: 1,
                        child: CardHighlight(child: WorkDatagrid()),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
