import 'package:flutter/widgets.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/accounting/widgets/monthly_report_datagrid.dart';
import 'package:workshop_studio/presentation/pages/cashflow/pages/accounting/widgets/yearly_report_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class AccountingPage extends StatelessWidget {
  const AccountingPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      spacing: 18,
      children: [
        Flexible(
          flex: 1,
          child: Column(
            spacing: 18,
            children: [
              CardHighlight(child: Text("Monthly reports")),
              Expanded(child: CardHighlight(child: MonthlyReportDataGrid())),
            ],
          ),
        ),
        Flexible(
          flex: 1,
          child: Column(
            spacing: 18,
            children: [
              CardHighlight(child: Text("Yearly reports")),
              Expanded(child: CardHighlight(child: YearlyReportDataGrid())),
            ],
          ),
        ),
      ],
    );
  }
}
