// clients_notifier.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:workshop_studio/providers/clients/search/search_query_notifier.dart';
import 'package:workshop_studio/providers/clients/timeline_filter_provider.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';
import 'package:workshop_studio/services/database_service.dart';

class ClientState {
  final List<ClientModel> clients;
  final bool isLoading;
  final int totalCount;
  final int currentPage;

  ClientState({
    required this.clients,
    required this.isLoading,
    required this.totalCount,
    required this.currentPage,
  });

  ClientState copyWith({
    List<ClientModel>? clients,
    bool? isLoading,
    int? totalCount,
    int? currentPage,
  }) {
    return ClientState(
      clients: clients ?? this.clients,
      isLoading: isLoading ?? this.isLoading,
      totalCount: totalCount ?? this.totalCount,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

final selectedClientProvider = StateProvider<ClientModel?>((ref) => null);

final clientProvider = StateNotifierProvider<ClientNotifier, ClientState>((
  ref,
) {
  final db = ref.read(mysqlServiceProvider);
  final filter = ref.watch(timelineFilterProviderFamily('clients'));
  final searchQuery = ref.watch(searchQueryProvider('clients'));
  return ClientNotifier(db, filter, searchQuery);
});

class ClientNotifier extends StateNotifier<ClientState> {
  final MySQLService _dbService;
  final TimelineFilter filter;
  final int _limit = 50;
  final String searchQuery;

  ClientNotifier(this._dbService, this.filter, this.searchQuery)
    : super(
        ClientState(
          clients: [],
          isLoading: true,
          totalCount: 0,
          currentPage: 0,
        ),
      ) {
    loadClients();
  }

  Future<void> loadClients() async {
    // Build base query conditions
    String whereClause = "";
    Map<String, dynamic> params = {};

    final isSearching = searchQuery.isNotEmpty;

    if (!isSearching && filter.type != TimelineFilterType.all) {
      String fromDate = "";
      String toDate = "";
      final now = DateTime.now();

      if (filter.type == TimelineFilterType.currentYear) {
        fromDate = DateFormat('yyyy-01-01').format(now);
        toDate = DateFormat('yyyy-12-31').format(now);
      } else if (filter.type == TimelineFilterType.currentMonth) {
        fromDate = DateFormat('yyyy-MM-01').format(now);
        toDate = DateFormat(
          'yyyy-MM-dd',
        ).format(DateTime(now.year, now.month + 1, 0));
      } else if (filter.type == TimelineFilterType.currentWeek) {
        final beginningOfWeek = now.subtract(Duration(days: now.weekday - 1));
        final endOfWeek = beginningOfWeek.add(const Duration(days: 6));
        fromDate = DateFormat('yyyy-MM-dd').format(beginningOfWeek);
        toDate = DateFormat('yyyy-MM-dd').format(endOfWeek);
      } else if (filter.type == TimelineFilterType.customRange &&
          filter.from != null &&
          filter.to != null) {
        fromDate = DateFormat('yyyy-MM-dd').format(filter.from!);
        toDate = DateFormat('yyyy-MM-dd').format(filter.to!);
      }

      whereClause = "WHERE date BETWEEN :from AND :to";
      params["from"] = fromDate;
      params["to"] = toDate;
    }

    if (isSearching) {
      whereClause = "WHERE c_name LIKE :query OR c_phone LIKE :query";
      params["query"] = '%$searchQuery%';
    }

    // Get total count
    final countResult = await _dbService.query(
      "SELECT COUNT(*) as total FROM clients $whereClause",
      params: params,
    );
    final totalCount = int.parse(countResult.rows.first.colByName('total')!);

    // Get paginated data
    final dataQuery = """
      SELECT * FROM clients 
      $whereClause 
      ORDER BY id DESC 
      LIMIT $_limit OFFSET ${state.currentPage * _limit}
    """;

    final dataResult = await _dbService.query(dataQuery, params: params);
    final clients =
        dataResult.rows.map((row) {
          return ClientModel.fromJson({
            'id': row.colByName('id')!,
            'c_name': row.colByName('c_name')!,
            'c_city': row.colByName('c_city') ?? '',
            'c_address': row.colByName('c_address') ?? '',
            'c_phone': row.colByName('c_phone') ?? '',
            'c_phone_2': row.colByName('c_phone_2') ?? '',
            'c_type': row.colByName('c_type') ?? '',
            'date': row.colByName('date') ?? '',
            'time': row.colByName('time') ?? '',
            'debts': row.colByName('debts') ?? '0',
            'user': row.colByName('user') ?? '',
            'c_email': row.colByName('c_email') ?? '',
            'c_rc': row.colByName('c_rc') ?? '',
            'c_nif': row.colByName('c_nif') ?? '',
            'c_ai': row.colByName('c_ai') ?? '',
            'c_nis': row.colByName('c_nis') ?? '',
            'c_tva': row.colByName('c_tva') ?? '',
          });
        }).toList();

    state = state.copyWith(
      clients: clients,
      isLoading: false,
      totalCount: totalCount,
    );
  }

  void setPage(int page) {
    if (state.currentPage == page) return;
    state = state.copyWith(currentPage: page);
    loadClients();
  }

  void reset() {
    state = state.copyWith(clients: [], currentPage: 0, totalCount: 0);
    loadClients();
  }

  void updateClientRow(int clientId, double newDebt) {
    final updatedClients =
        state.clients.map((client) {
          if (client.id == clientId) {
            return client.copyWith(debts: newDebt);
          }
          return client;
        }).toList();
    state = state.copyWith(clients: updatedClients);
  }
}
