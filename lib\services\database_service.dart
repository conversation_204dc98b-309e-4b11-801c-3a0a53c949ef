import 'package:mysql_client/mysql_client.dart';

class MySQLService {
  MySQLConnection? _connection;

  Future<MySQLConnection> get _ensureConnection async {
    if (_connection == null) {
      _connection = await MySQLConnection.createConnection(
        host: '127.0.0.1',
        port: 3306,
        userName: 'root',
        password: 'pcfix@2025',
        databaseName: 'workshop',
      );
      await _connection!.connect();
    }
    return _connection!;
  }

  Future<IResultSet> query(String sql, {Map<String, dynamic>? params}) async {
    final conn = await _ensureConnection;
    return await conn.execute(sql, params ?? {});
  }

  Future<void> close() async {
    await _connection?.close();
  }
}
