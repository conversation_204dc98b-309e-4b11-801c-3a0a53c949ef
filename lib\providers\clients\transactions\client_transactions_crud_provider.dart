import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/models/client/client_sold_model.dart';
import 'package:workshop_studio/services/database_service.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';

final clientTransactionsCrudProvider = Provider<ClientTransactionsCRUD>((ref) {
  final dbService = ref.read(mysqlServiceProvider);
  return ClientTransactionsCRUD(dbService);
});

class ClientTransactionsCRUD {
  final MySQLService _dbService;

  ClientTransactionsCRUD(this._dbService);

  // CREATE Transaction (with TVA condition)
  Future<int> createTransaction(ClientSoldModel transaction, bool cTva) async {
    final params = transaction.toJson();

    if (cTva) {
      final result = await _dbService.query('''
        INSERT INTO client_soldtva (
          c_id, b_id, machines_ids, payment_type, tht, tvav, ttva, ttc,
          current_balance, payment, old_balance, new_balance, note,
          date, time, date_warranty
        ) VALUES (
          :c_id, :b_id, :machines_ids, :payment_type, :tht, :tvav, :ttva, :ttc,
          :current_balance, :payment, :old_balance, :new_balance, :note,
          CURDATE(), CURTIME(), :date_warranty
        )
      ''', params: params);
      return result.lastInsertID.toInt();
    } else {
      final result = await _dbService.query('''
        INSERT INTO client_sold (
          c_id, b_id, machines_ids, payment_type,
          current_balance, payment, old_balance, new_balance, note,
          date, time, date_warranty
        ) VALUES (
          :c_id, :b_id, :machines_ids, :payment_type,
          :current_balance, :payment, :old_balance, :new_balance, :note,
          CURDATE(), CURTIME(), :date_warranty
        )
      ''', params: params);
      return result.lastInsertID.toInt();
    }
  }

  // UPDATE Transaction (with TVA condition)
  Future<bool> updateTransaction(ClientSoldModel transaction, bool cTva) async {
    final params = transaction.toJson();

    if (cTva) {
      final result = await _dbService.query('''
        UPDATE client_soldtva SET
          b_id = :b_id,
          machines_ids = :machines_ids,
          payment_type = :payment_type,
          tht = :tht,
          tvav = :tvav,
          ttva = :ttva,
          ttc = :ttc,
          current_balance = :current_balance,
          payment = :payment,
          old_balance = :old_balance,
          new_balance = :new_balance,
          note = :note,
          date_warranty = :date_warranty
        WHERE id = :s_id
      ''', params: params);
      return result.affectedRows.toInt() > 0;
    } else {
      final result = await _dbService.query('''
        UPDATE client_sold SET
          b_id = :b_id,
          machines_ids = :machines_ids,
          payment_type = :payment_type,
          current_balance = :current_balance,
          payment = :payment,
          old_balance = :old_balance,
          new_balance = :new_balance,
          note = :note,
          date_warranty = :date_warranty
        WHERE s_id = :s_id
      ''', params: params);
      return result.affectedRows.toInt() > 0;
    }
  }

  // DELETE Transaction (with TVA condition)
  Future<bool> deleteTransaction(int transactionId, bool cTva) async {
    final table = cTva ? 'client_soldtva' : 'client_sold';
    final idColumn = cTva ? 'id' : 's_id';

    final result = await _dbService.query(
      'DELETE FROM $table WHERE $idColumn = :transaction_id',
      params: {'transaction_id': transactionId},
    );
    return result.affectedRows.toInt() > 0;
  }
}
