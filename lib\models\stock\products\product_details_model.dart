class ProductDetailsModel {
  ProductDetailsModel({
    required this.purchasePrice,
    required this.cump,
    required this.reailPrice,
    required this.resellerPrice,
    required this.quantity,
    required this.quantityAlert,
    required this.supplier,
    required this.date,
    required this.updatedUser,
    required this.updatedDate,
  });
  final double purchasePrice;
  final double cump;
  final double reailPrice;
  final double resellerPrice;
  final int quantity;
  final int quantityAlert;
  final String supplier;
  final DateTime date;
  final String updatedUser;
  final DateTime updatedDate;
}
