import 'package:flutter_riverpod/flutter_riverpod.dart';

class ClientPaymentState {
  final double debt;
  final bool isLoading;

  ClientPaymentState({required this.debt, required this.isLoading});

  ClientPaymentState copyWith({double? debt, bool? isLoading}) {
    return ClientPaymentState(
      debt: debt ?? this.debt,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

class ClientPaymentNotifier extends StateNotifier<ClientPaymentState> {
  ClientPaymentNotifier()
    : super(ClientPaymentState(debt: 0.0, isLoading: false));

  void setInitialDebt(double debt) {
    state = state.copyWith(debt: debt);
  }

  void updateDebt(double payment) {
    state = state.copyWith(debt: state.debt - payment);
  }
}

final clientPaymentNotifierProvider =
    StateNotifierProvider<ClientPaymentNotifier, ClientPaymentState>((ref) {
      return ClientPaymentNotifier();
    });
