import 'package:go_router/go_router.dart';
import 'package:workshop_studio/presentation/app.dart';
import 'package:workshop_studio/presentation/app_login.dart';
import 'package:workshop_studio/presentation/app_setup.dart';

final GoRouter appRouter = GoRouter(
  routes: [
    GoRoute(path: '/', builder: (context, state) => MainScreen()),
    GoRoute(path: 'setup', builder: (context, state) => AppSetup()),
    GoRoute(path: 'login', builder: (context, state) => AppLogin()),
  ],
);
