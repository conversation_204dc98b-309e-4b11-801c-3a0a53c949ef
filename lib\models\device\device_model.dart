class DeviceModel {
  final int machineId;
  final String? clientName;
  final String? clientPhone;
  final int clientId;
  final int billId;
  final String machineType;
  final String machineBrand;
  final String? machineSerie;
  final String? machineModel;
  final String? serialNumber;
  final String? problem;
  final double estimatedPrice;
  final double price;
  final int warranty;
  final int emergency;
  final String technician;
  final int deadline;
  final String? note;
  final String date;
  final String time;
  final int phase;
  final String? dateUpdate;
  final int delivery;
  final String? dateDelivery;
  final int calculated;

  DeviceModel({
    required this.machineId,
    required this.clientName,
    required this.clientPhone,
    required this.clientId,
    required this.billId,
    required this.machineType,
    required this.machineBrand,
    this.machineSerie,
    this.machineModel,
    this.serialNumber,
    this.problem,
    required this.estimatedPrice,
    required this.price,
    required this.warranty,
    required this.emergency,
    required this.technician,
    required this.deadline,
    this.note,
    required this.date,
    required this.time,
    required this.phase,
    this.dateUpdate,
    required this.delivery,
    this.dateDelivery,
    required this.calculated,
  });

  factory DeviceModel.fromJson(Map<String, dynamic> json) {
    return DeviceModel(
      machineId: int.tryParse(json['m_id']?.toString() ?? '') ?? 0,
      clientName: json['c_name'] as String,
      clientPhone: json['c_phone'] as String,
      clientId: int.tryParse(json['c_id']?.toString() ?? '') ?? 0,
      billId: int.tryParse(json['b_id']?.toString() ?? '') ?? 0,
      machineType: json['m_type'] as String,
      machineBrand: json['m_brand'] as String,
      machineSerie: json['m_serie'] as String?,
      machineModel: json['m_model'] as String?,
      serialNumber: json['m_serial_num'] as String?,
      problem: json['m_problem'] as String?,
      estimatedPrice: double.parse(json['m_estimated_price'].toString()),
      price: double.parse(json['price'].toString()),
      warranty: int.tryParse(json['warranty'].toString()) ?? 0,
      emergency: int.tryParse(json['emergency'].toString()) ?? 0,
      technician: json['technician'] as String,
      deadline: int.tryParse(json['deadline'].toString()) ?? 0,
      note: json['note'] as String?,
      date: json['date'] as String,
      time: json['time'] as String,
      phase: int.tryParse(json['phase']?.toString() ?? '') ?? 0,
      dateUpdate: json['dateupdate'] as String?,
      delivery: int.tryParse(json['delivery'].toString()) ?? 0,
      calculated: int.tryParse(json['datedelivery'].toString()) ?? 0,
      dateDelivery: json['datedelivery'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'm_id': machineId,
      'c_name': clientName,
      'c_phone': clientPhone,
      'c_id': clientId,
      'b_id': billId,
      'm_type': machineType,
      'm_brand': machineBrand,
      'm_serie': machineSerie,
      'm_model': machineModel,
      'm_serial_num': serialNumber,
      'm_problem': problem,
      'm_estimated_price': estimatedPrice,
      'price': price,
      'warranty': warranty,
      'emergency': emergency,
      'technician': technician,
      'deadline': deadline,
      'note': note,
      'date': date,
      'time': time,
      'phase': phase,
      'dateupdate': dateUpdate,
      'delivery': delivery,
      'datedelivery': dateDelivery,
      'calculated': calculated,
    };
  }

  DeviceModel copyWith({
    int? machineId,
    String? clientName,
    String? clientPhone,
    int? clientId,
    int? billId,
    String? machineType,
    String? machineBrand,
    String? machineSerie,
    String? machineModel,
    String? serialNumber,
    String? problem,
    double? estimatedPrice,
    double? price,
    int? warranty,
    int? emergency,
    String? technician,
    int? deadline,
    String? note,
    String? date,
    String? time,
    int? phase,
    String? dateUpdate,
    int? delivery,
    String? dateDelivery,
    int? calculated,
  }) {
    return DeviceModel(
      machineId: machineId ?? this.machineId,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      clientId: clientId ?? this.clientId,
      billId: billId ?? this.billId,
      machineType: machineType ?? this.machineType,
      machineBrand: machineBrand ?? this.machineBrand,
      machineSerie: machineSerie ?? this.machineSerie,
      machineModel: machineModel ?? this.machineModel,
      serialNumber: serialNumber ?? this.serialNumber,
      problem: problem ?? this.problem,
      estimatedPrice: estimatedPrice ?? this.estimatedPrice,
      price: price ?? this.price,
      warranty: warranty ?? this.warranty,
      emergency: emergency ?? this.emergency,
      technician: technician ?? this.technician,
      deadline: deadline ?? this.deadline,
      note: note ?? this.note,
      date: date ?? this.date,
      time: time ?? this.time,
      phase: phase ?? this.phase,
      dateUpdate: dateUpdate ?? this.dateUpdate,
      delivery: delivery ?? this.delivery,
      dateDelivery: dateDelivery ?? this.dateDelivery,
      calculated: calculated ?? this.calculated,
    );
  }
}
