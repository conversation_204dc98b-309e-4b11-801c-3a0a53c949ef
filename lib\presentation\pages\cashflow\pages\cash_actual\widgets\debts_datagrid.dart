import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
// import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/cash_actual/client_debts_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class DebtsDataGrid extends StatefulWidget {
  const DebtsDataGrid({super.key});

  @override
  State<DebtsDataGrid> createState() => _DebtsDataGridState();
}

class _DebtsDataGridState extends State<DebtsDataGrid> {
  List<ClientDebtsTableModel> employees = <ClientDebtsTableModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    // final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(headerColor: Colors.blue.lightest),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'number',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("N°", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'client',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("CLIENT", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'debts',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("CURRENT DEBTS", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'time',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TIME", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<ClientDebtsTableModel> getEmployeeData() {
    return [
      ClientDebtsTableModel(
        id: 1,
        client: "HAMZA HALFI",
        debts: 800.00,
        time: DateTime.now(),
      ),
      ClientDebtsTableModel(
        id: 1,
        client: "HAMZA HALFI",
        debts: 800.00,
        time: DateTime.now(),
      ),
      ClientDebtsTableModel(
        id: 1,
        client: "HAMZA HALFI",
        debts: 800.00,
        time: DateTime.now(),
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<ClientDebtsTableModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'id', value: e.id),
                  DataGridCell<String>(columnName: 'client', value: e.client),
                  DataGridCell<double>(columnName: 'debts', value: e.debts),
                  DataGridCell<DateTime>(columnName: 'time', value: e.time),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
