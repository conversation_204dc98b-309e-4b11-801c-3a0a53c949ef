import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/vat_transactions/vat_transactions_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class TransactionsDatagrid extends StatefulWidget {
  const TransactionsDatagrid({super.key});

  @override
  State<TransactionsDatagrid> createState() => _TransactionsDatagridState();
}

class _TransactionsDatagridState extends State<TransactionsDatagrid> {
  List<VatTransactionsTableModel> employees = <VatTransactionsTableModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    return SfDataGridTheme(
      data: SfDataGridThemeData(
        headerColor: FluentThemeData().accentColor.lightest,
      ),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerRowHeight: 80,
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResizeEnd,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'n',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("N°", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'client',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.client, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'current_balance',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("CURRENT BALANCE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'old_balance',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("OLD BALANCE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'total',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TOTAL", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'payment',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("PAYMENT", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'type',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TYPE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'new_balance',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("NEW BALANCE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'date',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("DATE", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'time',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TIME", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'end_of_warranty',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("END OF WARRANTY", style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'transaction_note',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text("TRANSACTION NOTE", style: headerTextStyle),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<VatTransactionsTableModel> getEmployeeData() {
    return [];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<VatTransactionsTableModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'n', value: e.id),
                  DataGridCell<String>(columnName: 'client', value: e.client),
                  // DataGridCell<double>(
                  //   columnName: 'current_balance',
                  //   value: e.currentBalance,
                  // ),
                  // DataGridCell<double>(
                  //   columnName: 'old_balance',
                  //   value: e.oldBalance,
                  // ),
                  // DataGridCell<double>(columnName: 'total', value: e.total),
                  // DataGridCell<double>(columnName: 'payment', value: e.payment),
                  DataGridCell<int>(columnName: 'type', value: e.type),
                  // DataGridCell<double>(
                  //   columnName: 'new_balance',
                  //   value: e.newBalance,
                  // ),
                  // DataGridCell<String>(columnName: 'date', value: e.date),
                  // DataGridCell<String>(columnName: 'time', value: e.time),
                  DataGridCell<int>(
                    columnName: 'end_of_warranty',
                    value: e.endOfWarranty,
                  ),
                  DataGridCell<String>(
                    columnName: 'transaction_note',
                    value: e.transactionNote,
                  ),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child: Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
