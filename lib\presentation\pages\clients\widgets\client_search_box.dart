import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/providers/clients/search/search_query_notifier.dart';

class ClientSearchBox extends ConsumerStatefulWidget {
  const ClientSearchBox({super.key});

  @override
  ConsumerState<ClientSearchBox> createState() => _ClientSearchBoxState();
}

class _ClientSearchBoxState extends ConsumerState<ClientSearchBox> {
  TextEditingController? _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  void _safeResetClients() {
    if (!mounted) return;
    // Use a microtask to ensure this happens after the current frame
    Future.microtask(() {
      if (!mounted) return;
      ref.read(clientProvider.notifier).reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    final search = ref.watch(searchQueryProvider('clients'));
    final notifier = ref.read(searchQueryProvider('clients').notifier);

    // Update controller text if it doesn't match current search
    if (_controller?.text != search) {
      _controller?.text = search;
      _controller?.selection = TextSelection.fromPosition(
        TextPosition(offset: search.length),
      );
    }

    return TextBox(
      placeholder: 'Search by name or phone',
      controller: _controller,
      onChanged: (value) {
        if (mounted) {
          notifier.setQuery(value);
          _safeResetClients();
        }
      },
      prefix: Padding(
        padding: const EdgeInsets.all(8),
        child: Icon(FluentIcons.profile_search),
      ),
      suffix: IconButton(
        icon: const Icon(FluentIcons.clear),
        onPressed: () {
          if (mounted) {
            notifier.clear();
            _safeResetClients();
          }
        },
      ),
    );
  }
}
