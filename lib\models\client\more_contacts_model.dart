class MoreContactsModel {
  final int id;
  final int cId;
  final String fullName;
  final String phone;
  final String jobTitle;

  MoreContactsModel({
    required this.id,
    required this.cId,
    required this.fullName,
    required this.phone,
    required this.jobTitle,
  });

  MoreContactsModel copyWith({
    int? id,
    int? cId,
    String? fullName,
    String? phone,
    String? jobTitle,
  }) {
    return MoreContactsModel(
      id: id ?? this.id,
      cId: cId ?? this.cId,
      fullName: fullName ?? this.fullName,
      phone: phone ?? this.phone,
      jobTitle: jobTitle ?? this.jobTitle,
    );
  }

  factory MoreContactsModel.fromJson(Map<String, dynamic> json) {
    return MoreContactsModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      cId: int.tryParse(json['c_id']?.toString() ?? '') ?? 0,
      fullName: json['full_name'] ?? '',
      jobTitle: json['job_title'] ?? '',
      phone: json['phone_n'] ?? '',
    );
  }
}
