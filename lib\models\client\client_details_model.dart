class ClientDetailsModel {
  ClientDetailsModel({
    required this.city,
    required this.address,
    required this.email,
    required this.rc,
    required this.nif,
    required this.ai,
    required this.nis,
    required this.tva,
  });
  final String city;
  final String address;
  final String email;
  final String rc;
  final String nif;
  final String ai;
  final String nis;
  final bool tva;

  factory ClientDetailsModel.fromJson(Map<String, dynamic> json) {
    return ClientDetailsModel(
      city: json['c_city'] as String,
      address: json['c_address'] as String,
      email: json['c_email'] as String,
      rc: json['c_rc'] as String,
      nif: json['c_nif'] as String,
      ai: json['c_ai'] as String,
      nis: json['c_nis'] as String,
      tva: json['c_tva'] as bool,
    );
  }
}
