import 'package:fluent_ui/fluent_ui.dart';

class ProductsDetailsDatagrid extends StatefulWidget {
  const ProductsDetailsDatagrid({super.key});

  @override
  State<ProductsDetailsDatagrid> createState() =>
      _ProductsDetailsDatagridState();
}

class _ProductsDetailsDatagridState extends State<ProductsDetailsDatagrid> {
  List<String> titles = [
    "Purchase price",
    "CUMP",
    "Retail price",
    "Reseller price",
    "Quantity",
    "Supplier",
    "Technician",
    "Date",
    "Date of update",
    "User",
  ];
  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 10,
      itemBuilder:
          (BuildContext context, index) => ListTile(
            contentAlignment: CrossAxisAlignment.start,
            onPressed: () {},
            leading: Text(
              titles[index],
              style: TextStyle(fontSize: 13.5, fontWeight: FontWeight.w500),
            ),
            title: Text(
              "hamza halfi",
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w400,
                // color:
                //     FluentTheme.of(
                //       context,
                //     ).resources.controlStrongFillColorDefault,
              ),
            ),
          ),
    );
  }
}
