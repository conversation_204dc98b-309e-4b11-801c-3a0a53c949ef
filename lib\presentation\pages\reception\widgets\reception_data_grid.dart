import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/reception_table_model.dart';
import 'package:workshop_studio/presentation/utils/data_grid_clipper.dart';

class ReceptionDataGrid extends StatefulWidget {
  const ReceptionDataGrid({super.key});

  @override
  State<ReceptionDataGrid> createState() => _ReceptionDataGridState();
}

class _ReceptionDataGridState extends State<ReceptionDataGrid> {
  List<ReceptionDataModel> employees = <ReceptionDataModel>[];
  late EmployeeDataSource employeeDataSource;

  @override
  void initState() {
    super.initState();
    employees = getEmployeeData();
    employeeDataSource = EmployeeDataSource(employeeData: employees);
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle headerTextStyle = TextStyle(color: Colors.grey[10]);
    final lang = AppLocalizations.of(context);
    return SfDataGridTheme(
      data: SfDataGridThemeData(
        headerColor: Colors.blue.light,
        selectionColor: Colors.blue.lightest,
      ),
      child: ClipRect(
        clipper: DataGridClipper(),
        child: ScrollConfiguration(
          behavior: const ScrollBehavior().copyWith(scrollbars: false),
          child: SfDataGrid(
            headerGridLinesVisibility: GridLinesVisibility.none,
            gridLinesVisibility: GridLinesVisibility.none,
            source: employeeDataSource,
            columnWidthMode: ColumnWidthMode.fill,
            columnResizeMode: ColumnResizeMode.onResize,
            isScrollbarAlwaysShown: false,
            columns: <GridColumn>[
              GridColumn(
                columnName: 'id',
                label: Container(
                  padding: EdgeInsets.all(16.0),
                  alignment: Alignment.center,
                  child: Text(lang.id, style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'type',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.type.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'brand',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.brand.toUpperCase(),
                    style: headerTextStyle,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'serei',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.serie.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'model',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.model.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'serial_number',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.serial_n.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'issue',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(lang.issue.toUpperCase(), style: headerTextStyle),
                ),
              ),
              GridColumn(
                columnName: 'deadline',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.deadline.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'estimated_price',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.estimated_price.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'warranty',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.warranty.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'emergency',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.emergency.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
              GridColumn(
                columnName: 'remarks',
                label: Container(
                  padding: EdgeInsets.all(8.0),
                  alignment: Alignment.center,
                  child: Text(
                    lang.remarks.toUpperCase(),
                    style: headerTextStyle,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<ReceptionDataModel> getEmployeeData() {
    return [
      ReceptionDataModel(
        id: 202,
        type: "LAPTOP",
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "T400",
        serialNumber: "10025599066440488",
        issue: "AFFICHAGE + DIAG BOOT",
        deadline: 5,
        estimatedPrice: 5000,
        warranty: true,
        emergency: false,
        remarks: "SON CLAVIE + SON BATTERY + SCREEN BROKEN",
      ),
      ReceptionDataModel(
        id: 202,
        type: "LAPTOP",
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "T400",
        serialNumber: "10025599066440488",
        issue: "AFFICHAGE + DIAG BOOT",
        deadline: 5,
        estimatedPrice: 7000,
        warranty: false,
        emergency: false,
        remarks: "SON CLAVIE + SON BATTERY + SCREEN BROKEN",
      ),
      ReceptionDataModel(
        id: 202,
        type: "LAPTOP",
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "T400",
        serialNumber: "10025599066440488",
        issue: "AFFICHAGE + DIAG BOOT",
        deadline: 5,
        estimatedPrice: 3500,
        warranty: true,
        emergency: true,
        remarks: "SON CLAVIE + SON BATTERY + SCREEN BROKEN",
      ),
      ReceptionDataModel(
        id: 202,
        type: "LAPTOP",
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "T400",
        serialNumber: "10025599066440488",
        issue: "AFFICHAGE + DIAG BOOT",
        deadline: 5,
        estimatedPrice: 3500,
        warranty: true,
        emergency: true,
        remarks: "SON CLAVIE + SON BATTERY + SCREEN BROKEN",
      ),
      ReceptionDataModel(
        id: 202,
        type: "LAPTOP",
        brand: "LENOVO",
        serei: "THINKPAD",
        model: "T400",
        serialNumber: "10025599066440488",
        issue: "AFFICHAGE + DIAG BOOT",
        deadline: 5,
        estimatedPrice: 3500,
        warranty: true,
        emergency: true,
        remarks: "SON CLAVIE + SON BATTERY + SCREEN BROKEN",
      ),
    ];
  }
}

class EmployeeDataSource extends DataGridSource {
  EmployeeDataSource({required List<ReceptionDataModel> employeeData}) {
    _employeeData =
        employeeData
            .map<DataGridRow>(
              (e) => DataGridRow(
                cells: [
                  DataGridCell<int>(columnName: 'id', value: e.id),
                  DataGridCell<String>(columnName: 'type', value: e.type),
                  DataGridCell<String>(columnName: 'brand', value: e.brand),
                  DataGridCell<String>(columnName: 'serei', value: e.serei),
                  DataGridCell<String>(columnName: 'model', value: e.model),
                  DataGridCell<String>(
                    columnName: 'serial_number',
                    value: e.serialNumber,
                  ),
                  DataGridCell<String>(columnName: 'issue', value: e.issue),
                  DataGridCell<int>(columnName: 'deadline', value: e.deadline),
                  DataGridCell<double>(
                    columnName: 'estimated_price',
                    value: e.estimatedPrice,
                  ),
                  DataGridCell<bool>(columnName: 'warranty', value: e.warranty),
                  DataGridCell<bool>(
                    columnName: 'emergency',
                    value: e.emergency,
                  ),
                  DataGridCell<String>(columnName: 'remarks', value: e.remarks),
                ],
              ),
            )
            .toList();
  }

  List<DataGridRow> _employeeData = [];

  @override
  List<DataGridRow> get rows => _employeeData;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50);
      }

      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map<Widget>((e) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.all(8.0),
              child:
                  e.value.runtimeType == bool
                      ? e.value == true
                          ? Icon(
                            FluentIcons.accept,
                            color: Colors.successPrimaryColor,
                          )
                          : Icon(FluentIcons.cancel, color: Colors.red)
                      : Text(e.value.toString()),
            );
          }).toList(),
    );
  }
}
