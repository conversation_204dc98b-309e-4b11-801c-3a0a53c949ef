// clients_table_model.dart
class ClientModel {
  final int id;
  final String cName;
  final String cCity;
  final String cAddress;
  final String cPhone;
  final String cPhone2;
  final int cType;
  final String date;
  final String time;
  final double debts;
  final String user;
  final String cEmail;
  final String cRc;
  final String cNif;
  final String cAi;
  final String cNis;
  final bool cTva;

  ClientModel({
    required this.id,
    required this.cName,
    required this.cCity,
    required this.cAddress,
    required this.cPhone,
    required this.cPhone2,
    required this.cType,
    required this.date,
    required this.time,
    required this.debts,
    required this.user,
    required this.cEmail,
    required this.cRc,
    required this.cNif,
    required this.cAi,
    required this.cNis,
    required this.cTva,
  });

  ClientModel copyWith({
    int? id,
    String? cName,
    String? cCity,
    String? cAddress,
    String? cPhone,
    String? cPhone2,
    int? cType,
    String? date,
    String? time,
    double? debts,
    String? user,
    String? cEmail,
    String? cRc,
    String? cNif,
    String? cAi,
    String? cNis,
    bool? cTva,
  }) {
    return ClientModel(
      id: id ?? this.id,
      cName: cName ?? this.cName,
      cCity: cCity ?? this.cCity,
      cAddress: cAddress ?? this.cAddress,
      cPhone: cPhone ?? this.cPhone,
      cPhone2: cPhone2 ?? this.cPhone2,
      cType: cType ?? this.cType,
      date: date ?? this.date,
      time: time ?? this.time,
      debts: debts ?? this.debts,
      user: user ?? this.user,
      cEmail: cEmail ?? this.cEmail,
      cRc: cRc ?? this.cRc,
      cNif: cNif ?? this.cNif,
      cAi: cAi ?? this.cAi,
      cNis: cNis ?? this.cNis,
      cTva: cTva ?? this.cTva,
    );
  }

  factory ClientModel.fromJson(Map<String, dynamic> json) {
    return ClientModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      cName: json['c_name'] ?? '',
      cCity: json['c_city'] ?? '',
      cAddress: json['c_address'] ?? '',
      cPhone: json['c_phone'] ?? '',
      cPhone2: json['c_phone_2'] ?? '',
      cType: int.tryParse(json['c_type']?.toString() ?? '') ?? 0,
      date: json['date'] ?? '',
      time: json['time'] ?? '',
      debts: double.tryParse(json['debts']?.toString() ?? '') ?? 0.0,
      user: json['user']?.toString() ?? '',
      cEmail: json['c_email'] ?? '',
      cRc: json['c_rc'] ?? '',
      cNif: json['c_nif'] ?? '',
      cAi: json['c_ai'] ?? '',
      cNis: json['c_nis'] ?? '',
      cTva: (int.tryParse(json['c_tva']?.toString() ?? '') ?? 0) == 1,
    );
  }
}
