// SMS Templates Models for different repair statuses

/// Model for repaired SMS templates
class RepairedSmsModel {
  final int id;
  final String repaired;

  RepairedSmsModel({
    required this.id,
    required this.repaired,
  });

  factory RepairedSmsModel.fromJson(Map<String, dynamic> json) {
    return RepairedSmsModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      repaired: json['repaired'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'repaired': repaired,
    };
  }

  RepairedSmsModel copyWith({
    int? id,
    String? repaired,
  }) {
    return RepairedSmsModel(
      id: id ?? this.id,
      repaired: repaired ?? this.repaired,
    );
  }
}

/// Model for not repaired SMS templates
class NotRepairedSmsModel {
  final int id;
  final String notRepaired;

  NotRepairedSmsModel({
    required this.id,
    required this.notRepaired,
  });

  factory NotRepairedSmsModel.fromJson(Map<String, dynamic> json) {
    return NotRepairedSmsModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      notRepaired: json['not_repaired'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'not_repaired': notRepaired,
    };
  }

  NotRepairedSmsModel copyWith({
    int? id,
    String? notRepaired,
  }) {
    return NotRepairedSmsModel(
      id: id ?? this.id,
      notRepaired: notRepaired ?? this.notRepaired,
    );
  }
}

/// Model for to confirm SMS templates
class ToConfirmSmsModel {
  final int id;
  final String toConfirm;

  ToConfirmSmsModel({
    required this.id,
    required this.toConfirm,
  });

  factory ToConfirmSmsModel.fromJson(Map<String, dynamic> json) {
    return ToConfirmSmsModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      toConfirm: json['to_confirm'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'to_confirm': toConfirm,
    };
  }

  ToConfirmSmsModel copyWith({
    int? id,
    String? toConfirm,
  }) {
    return ToConfirmSmsModel(
      id: id ?? this.id,
      toConfirm: toConfirm ?? this.toConfirm,
    );
  }
}

/// Model for missing pieces SMS templates
class MissingPiecesSmsModel {
  final int id;
  final String missingPieces;

  MissingPiecesSmsModel({
    required this.id,
    required this.missingPieces,
  });

  factory MissingPiecesSmsModel.fromJson(Map<String, dynamic> json) {
    return MissingPiecesSmsModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      missingPieces: json['missing_pieces'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'missing_pieces': missingPieces,
    };
  }

  MissingPiecesSmsModel copyWith({
    int? id,
    String? missingPieces,
  }) {
    return MissingPiecesSmsModel(
      id: id ?? this.id,
      missingPieces: missingPieces ?? this.missingPieces,
    );
  }
}

/// Model for rejected SMS templates
class RejectedSmsModel {
  final int id;
  final String rejected;

  RejectedSmsModel({
    required this.id,
    required this.rejected,
  });

  factory RejectedSmsModel.fromJson(Map<String, dynamic> json) {
    return RejectedSmsModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      rejected: json['rejected'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rejected': rejected,
    };
  }

  RejectedSmsModel copyWith({
    int? id,
    String? rejected,
  }) {
    return RejectedSmsModel(
      id: id ?? this.id,
      rejected: rejected ?? this.rejected,
    );
  }
}

/// Enum for SMS template types
enum SmsTemplateType {
  repaired,
  notRepaired,
  toConfirm,
  missingPieces,
  rejected,
}

/// Extension for SmsTemplateType to get table names
extension SmsTemplateTypeExtension on SmsTemplateType {
  String get tableName {
    switch (this) {
      case SmsTemplateType.repaired:
        return 'repaired_sms';
      case SmsTemplateType.notRepaired:
        return 'not_repaired_sms';
      case SmsTemplateType.toConfirm:
        return 'to_confirm_sms';
      case SmsTemplateType.missingPieces:
        return 'missing_pieces_sms';
      case SmsTemplateType.rejected:
        return 'rejected_sms';
    }
  }

  String get columnName {
    switch (this) {
      case SmsTemplateType.repaired:
        return 'repaired';
      case SmsTemplateType.notRepaired:
        return 'not_repaired';
      case SmsTemplateType.toConfirm:
        return 'to_confirm';
      case SmsTemplateType.missingPieces:
        return 'missing_pieces';
      case SmsTemplateType.rejected:
        return 'rejected';
    }
  }

  String get displayName {
    switch (this) {
      case SmsTemplateType.repaired:
        return 'Repaired';
      case SmsTemplateType.notRepaired:
        return 'Not Repaired';
      case SmsTemplateType.toConfirm:
        return 'To Confirm';
      case SmsTemplateType.missingPieces:
        return 'Missing Pieces';
      case SmsTemplateType.rejected:
        return 'Rejected';
    }
  }
}
