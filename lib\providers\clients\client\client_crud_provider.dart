// client_crud_provider.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/providers/mysql_connection_provider.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';
import 'package:workshop_studio/services/database_service.dart';

final clientCrudProvider = Provider<ClientCRUD>((ref) {
  final dbService = ref.read(mysqlServiceProvider);
  return ClientCRUD(dbService);
});

class ClientCRUD {
  final MySQLService _dbService;

  ClientCRUD(this._dbService);

  // Create - Using prepared statement
  Future<int> createClient(ClientModel client) async {
    final result = await _dbService.query(
      '''
      INSERT INTO clients (
        c_name, c_city, c_address, c_phone, c_phone_2, c_type, date, time, 
        debts, user, c_email, c_rc, c_nif, c_ai, c_nis, c_tva
      ) VALUES (
        :name, :city, :address, :phone1, :phone2, :type, CURDATE(), CURTIME(), 
        :debts, :user, :email, :rc, :nif, :ai, :nis, :tva
      )
    ''',
      params: {
        'name': client.cName,
        'city': client.cCity,
        'address': client.cAddress,
        'phone1': client.cPhone,
        'phone2': client.cPhone2,
        'type': client.cType,
        'debts': client.debts,
        'user': client.user,
        'email': client.cEmail,
        'rc': client.cRc,
        'nif': client.cNif,
        'ai': client.cAi,
        'nis': client.cNis,
        'tva': client.cTva ? 1 : 0,
      },
    );

    return result.lastInsertID.toInt();
  }

  // Read - Already implemented in clients_notifier.dart
  // Update with prepared statement
  Future<bool> updateClient(ClientModel client) async {
    final result = await _dbService.query(
      '''
      UPDATE clients SET
        c_name = :name,
        c_city = :city,
        c_address = :address,
        c_phone = :phone1,
        c_phone_2 = :phone2,
        c_type = :type,
        debts = :debts,
        c_email = :email,
        c_rc = :rc,
        c_nif = :nif,
        c_ai = :ai,
        c_nis = :nis,
        c_tva = :tva
      WHERE id = :id
    ''',
      params: {
        'id': client.id,
        'name': client.cName,
        'city': client.cCity,
        'address': client.cAddress,
        'phone1': client.cPhone,
        'phone2': client.cPhone2,
        'type': client.cType,
        'debts': client.debts,
        'email': client.cEmail,
        'rc': client.cRc,
        'nif': client.cNif,
        'ai': client.cAi,
        'nis': client.cNis,
        'tva': client.cTva ? 1 : 0,
      },
    );

    return result.affectedRows.toInt() > 0;
  }

  // Delete with prepared statement
  Future<bool> deleteClient(int clientId) async {
    final result = await _dbService.query(
      '''
        SET FOREIGN_KEY_CHECKS=0;
        DELETE FROM clients WHERE id = :id;
        DELETE FROM clients_bills WHERE c_id = :id;
        SET FOREIGN_KEY_CHECKS=1;
        ''',

      params: {'id': clientId},
    );
    return result.affectedRows.toInt() > 0;
  }

  // Get single client
  Future<ClientModel?> getClient(int clientId) async {
    final result = await _dbService.query(
      'SELECT * FROM clients WHERE id = :id LIMIT 1',
      params: {'id': clientId},
    );

    if (result.rows.isEmpty) return null;

    return ClientModel.fromJson(result.rows.first.typedAssoc());
  }
}
