import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_crud_provider.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_notifier.dart';

void showDevicePhaseDialog(
  BuildContext context,
  WidgetRef ref,
  // DeviceModel device,
) {
  final lang = AppLocalizations.of(context);
  final dialogWidth = MediaQuery.of(context).size.width / 1.7;
  final dialogHeight = MediaQuery.of(context).size.height / 1.10;
  final device = ref.read(selectedDeviceProvider);
  // Controllers for form fields
  final estimatedPriceController = TextEditingController(
    text: device!.estimatedPrice.toStringAsFixed(2),
  );
  final priceController = TextEditingController(
    text: device.price.toStringAsFixed(2),
  );
  final smsController = TextEditingController();
  final remarksController = TextEditingController();
  final TextStyle headerTextStyle = TextStyle(fontWeight: FontWeight.w600);
  // providers
  final deviceCrud = ref.read(deviceCrudProvider('devicesAtWorkshop'));

  // State variables
  String oldTechnician = '';
  String technician = '';
  String phase = device.phase.toString();
  List<Map> availableTechnicians = [];
  bool isLoadingTechnicians = true;
  bool isPriceQuery = false;
  bool isTechnicianQuery = false;
  bool technicianSetter = false;
  bool phaseSetter = false;
  bool isPhaseQuery = false;
  bool isRemarksQuery = false;
  bool isSmsQuery = false;
  bool isLoadingRemarks = true;

  double devicePrice = device.price;

  // Update price controller to reflect current device price
  priceController.text = devicePrice.toStringAsFixed(2);

  final List<String> phases = [
    lang.on_hold,
    lang.diagnostic,
    lang.confirmation,
    lang.confirmed,
    lang.repaired,
    lang.not_repaired,
    lang.parts_unavailable,
    lang.rejected,
  ];

  showDialog(
    context: context,
    builder:
        (context) => StatefulBuilder(
          builder: (context, setState) {
            // Initialize technicians when dialog opens
            if (isLoadingTechnicians) {
              ref
                  .read(deviceProvider('devicesAtWorkshop').notifier)
                  .getAllTechnicians()
                  .then((technicians) {
                    setState(() {
                      availableTechnicians = technicians;
                      isLoadingTechnicians = false;
                    });
                  });
              ref
                  .read(deviceProvider('devicesAtWorkshop').notifier)
                  .getTechnicianForDevice(device.machineId)
                  .then((technicianName) {
                    setState(() {
                      // Find the technician ID by matching the name
                      if (technicianName != null) {
                        final matchingTech = availableTechnicians.firstWhere(
                          (tech) => tech['name'] == technicianName,
                          orElse: () => <String, dynamic>{},
                        );
                        if (matchingTech.isNotEmpty) {
                          technician = matchingTech['id'].toString();
                          oldTechnician = matchingTech['id'].toString();
                        }
                      }
                    });
                  });
            }

            // Initialize remarks when dialog opens
            if (isLoadingRemarks) {
              deviceCrud.getRemarks(machineId: device.machineId).then((
                remarks,
              ) {
                setState(() {
                  if (remarks != null && remarks.isNotEmpty) {
                    remarksController.text = remarks;
                  }
                  isLoadingRemarks = false;
                });
              });
            }

            return ContentDialog(
              constraints: BoxConstraints.expand(
                width: dialogWidth,
                height: dialogHeight,
              ),
              title: Text(
                lang.device_managment,
                style: TextStyle(fontSize: 18),
              ),
              content: ScaffoldPage.scrollable(
                children: [
                  CardHighlight(
                    child: Table(
                      children: [
                        TableRow(
                          children: [
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  lang.receipt_n,
                                  style: headerTextStyle,
                                ),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  lang.client.toUpperCase(),
                                  style: headerTextStyle,
                                ),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  lang.phone_n.toUpperCase(),
                                  style: headerTextStyle,
                                ),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  lang.type.toUpperCase(),
                                  style: headerTextStyle,
                                ),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  lang.brand.toUpperCase(),
                                  style: headerTextStyle,
                                ),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(lang.serie, style: headerTextStyle),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  lang.model.toUpperCase(),
                                  style: headerTextStyle,
                                ),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  lang.phase.toUpperCase(),
                                  style: headerTextStyle,
                                ),
                              ),
                            ),
                          ],
                        ),
                        TableRow(
                          children: [
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text('${device.billId}'),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text('${device.clientName}'),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text('${device.clientPhone}'),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(device.machineType),
                              ),
                            ),

                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(device.machineBrand),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(device.machineSerie.toString()),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text('${device.machineModel}'),
                              ),
                            ),
                            TableCell(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Text(
                                  phaseSetter
                                      ? phases[int.parse(phase)]
                                      : phases[device.phase],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  Divider(),
                  const SizedBox(height: 24),
                  // Device Price Section
                  _buildSectionTitle(lang.device_price),
                  const SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Expanded(
                        flex: 1,
                        child: InfoLabel(
                          label: lang.estimated_price,
                          child: TextBox(
                            controller: estimatedPriceController,
                            keyboardType: TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d+\.?\d{0,2}'),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(width: 16),
                      SizedBox(
                        height: 33.5,
                        child: Tooltip(
                          message: lang.est_to_final,
                          child: IconButton(
                            icon: Icon(CarbonIcons.arrow_down_left, size: 24),
                            onPressed: () {
                              setState(
                                () =>
                                    priceController.text =
                                        device.estimatedPrice.toString(),
                              );
                            },
                          ),
                        ),
                      ),
                      Spacer(flex: 3),
                    ],
                  ),
                  SizedBox(height: 8),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Flexible(
                        flex: 1,
                        child: InfoLabel(
                          label: lang.final_price,
                          child: TextBox(
                            controller: priceController,
                            keyboardType: TextInputType.numberWithOptions(
                              decimal: true,
                            ),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'^\d+\.?\d{0,2}'),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        flex: 1,
                        child: SizedBox(
                          height: 32,
                          // set or update price
                          child: Button(
                            child: Text(
                              devicePrice == 0
                                  ? '${lang.set} ${lang.price}'
                                  : '${lang.update} ${lang.price}',
                            ),
                            onPressed: () async {
                              // Validate input
                              final finalPrice = double.tryParse(
                                priceController.text,
                              );
                              final estimatedPrice = double.tryParse(
                                estimatedPriceController.text,
                              );

                              if (finalPrice == null || finalPrice < 0) {
                                if (context.mounted) {
                                  displayInfoBar(
                                    context,
                                    builder: (context, close) {
                                      return InfoBar(
                                        title: Text(
                                          '${lang.price} ${lang.error}',
                                        ),
                                        content: Text(
                                          '${lang.enter} valid ${lang.final_price}',
                                        ),
                                        severity: InfoBarSeverity.warning,
                                      );
                                    },
                                  );
                                }
                                return;
                              }

                              setState(() => isPriceQuery = true);
                              await Future.delayed(Duration(milliseconds: 200));
                              try {
                                bool updated = await deviceCrud.updatePrice(
                                  device.machineId,
                                  finalPrice,
                                  estimatedPrice ?? 0.00,
                                );
                                if (updated) {
                                  setState(() {
                                    isPriceQuery = false;
                                    // Update the devicePrice to reflect the new price
                                    devicePrice = finalPrice;
                                    // Update the controller text to reflect the new price
                                    priceController.text = finalPrice
                                        .toStringAsFixed(2);
                                  });

                                  // Update the selected device with the new price
                                  final updatedDevice = device.copyWith(
                                    price: finalPrice,
                                    estimatedPrice:
                                        estimatedPrice ?? device.estimatedPrice,
                                  );
                                  ref
                                      .read(selectedDeviceProvider.notifier)
                                      .state = updatedDevice;

                                  // Refresh the device list to reflect the changes
                                  ref
                                      .read(
                                        deviceProvider(
                                          'devicesAtWorkshop',
                                        ).notifier,
                                      )
                                      .loadDevices();

                                  // Show success message
                                  if (context.mounted) {
                                    displayInfoBar(
                                      context,
                                      builder: (context, close) {
                                        return InfoBar(
                                          title: Text(
                                            '${lang.price} ${lang.update}',
                                          ),
                                          content: Text(
                                            lang.priceUpdatedSuccessfully,
                                          ),
                                          severity: InfoBarSeverity.success,
                                        );
                                      },
                                    );
                                  }
                                } else {
                                  setState(() => isPriceQuery = false);
                                  // Show error message
                                  if (context.mounted) {
                                    displayInfoBar(
                                      context,
                                      builder: (context, close) {
                                        return InfoBar(
                                          title: Text(
                                            '${lang.price} ${lang.error}',
                                          ),
                                          content: Text(
                                            lang.failedToUpdatePrice,
                                          ),
                                          severity: InfoBarSeverity.error,
                                        );
                                      },
                                    );
                                  }
                                }
                              } catch (e) {
                                setState(() => isPriceQuery = false);
                                // Handle error
                                if (context.mounted) {
                                  displayInfoBar(
                                    context,
                                    builder: (context, close) {
                                      return InfoBar(
                                        title: Text(
                                          '${lang.price} ${lang.error}',
                                        ),
                                        content: Text(
                                          lang.failedToUpdatePriceWithError(
                                            e.toString(),
                                          ),
                                        ),
                                        severity: InfoBarSeverity.error,
                                      );
                                    },
                                  );
                                }
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 14),
                      isPriceQuery
                          ? SizedBox(
                            height: 28,
                            width: 28,
                            child: ProgressRing(),
                          )
                          : SizedBox(height: 28, width: 28),
                      Spacer(flex: 2),
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Technician Section
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Flexible(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle(lang.technician),
                            SizedBox(height: 8),
                            ComboBox<String>(
                              value:
                                  availableTechnicians.any(
                                        (tech) =>
                                            tech['id'].toString() == technician,
                                      )
                                      ? technician
                                      : availableTechnicians.isNotEmpty
                                      ? availableTechnicians.first['id']
                                          .toString()
                                      : null,
                              items:
                                  isLoadingTechnicians
                                      ? [
                                        ComboBoxItem<String>(
                                          value: 'loading',
                                          child: Text(
                                            '${lang.loading} ${lang.technicians}...',
                                          ),
                                        ),
                                      ]
                                      : availableTechnicians.isEmpty
                                      ? [
                                        ComboBoxItem<String>(
                                          value: 'no_technicians',
                                          child: Text(
                                            '${lang.no} ${lang.technicians} ${lang.found}',
                                          ),
                                        ),
                                      ]
                                      : availableTechnicians
                                          .map(
                                            (
                                              technician,
                                            ) => ComboBoxItem<String>(
                                              value:
                                                  technician['id'].toString(),
                                              child: Text(
                                                technician['name'] as String,
                                              ),
                                            ),
                                          )
                                          .toList(),
                              onChanged:
                                  isLoadingTechnicians
                                      ? null
                                      : (value) {
                                        technicianSetter = false;
                                        if (value != null &&
                                            value != 'loading' &&
                                            value != 'no_technicians') {
                                          setState(() {
                                            technician = value;
                                          });
                                        }
                                      },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 4),
                      Flexible(
                        flex: 1,
                        child: SizedBox(
                          height: 33.5,
                          child: Button(
                            child:
                                oldTechnician.isEmpty
                                    ? Text('${lang.set} ${lang.technician}')
                                    : Text('${lang.update} ${lang.technician}'),
                            onPressed: () async {
                              setState(() => isTechnicianQuery = true);
                              await Future.delayed(Duration(milliseconds: 200));
                              try {
                                // Get technician name from the selected technician
                                final selectedTech = availableTechnicians
                                    .firstWhere(
                                      (tech) =>
                                          tech['id'].toString() == technician,
                                      orElse: () => {'name': 'Unknown'},
                                    );
                                final technicianName =
                                    selectedTech['name'] ?? 'Unknown';

                                bool
                                updated = await deviceCrud.updateTechnician(
                                  workerId: int.parse(technician),
                                  clientId: device.clientId,
                                  machineId: device.machineId,
                                  phase: int.parse(phase),
                                  user:
                                      'Hamza Halfi', // TODO: Replace with actual user from auth system
                                  technician: technicianName,
                                  date: DateTime.now(),
                                  time:
                                      DateTime.now()
                                          .toString()
                                          .split(' ')[1]
                                          .split('.')[0], // HH:MM:SS format
                                );
                                if (updated) {
                                  setState(() {
                                    isTechnicianQuery = false;
                                    technicianSetter = true;
                                    oldTechnician =
                                        technician; // Update oldTechnician to reflect the new current technician
                                  });
                                  // Refresh the device list to reflect the changes
                                  ref
                                      .read(
                                        deviceProvider(
                                          'devicesAtWorkshop',
                                        ).notifier,
                                      )
                                      .loadDevices();

                                  // Show success message
                                  if (context.mounted) {
                                    displayInfoBar(
                                      context,
                                      builder: (context, close) {
                                        return InfoBar(
                                          title: Text(
                                            '${lang.technician} ${lang.update}',
                                          ),
                                          content: Text(
                                            lang.technicianAssignedSuccessfully,
                                          ),
                                          severity: InfoBarSeverity.success,
                                        );
                                      },
                                    );
                                  }
                                } else {
                                  setState(() => isTechnicianQuery = false);
                                  // Show error message
                                  if (context.mounted) {
                                    displayInfoBar(
                                      context,
                                      builder: (context, close) {
                                        return InfoBar(
                                          title: Text(
                                            '${lang.technician} ${lang.error}',
                                          ),
                                          content: Text(
                                            lang.failedToAssignTechnician,
                                          ),
                                          severity: InfoBarSeverity.error,
                                        );
                                      },
                                    );
                                  }
                                }
                              } catch (e) {
                                setState(() => isTechnicianQuery = false);
                                // Handle error
                                if (context.mounted) {
                                  displayInfoBar(
                                    context,
                                    builder: (context, close) {
                                      return InfoBar(
                                        title: Text(
                                          '${lang.technician} ${lang.error}',
                                        ),
                                        content: Text(
                                          lang.failedToAssignTechnicianWithError(
                                            e.toString(),
                                          ),
                                        ),
                                        severity: InfoBarSeverity.error,
                                      );
                                    },
                                  );
                                }
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(width: 14),
                      if (isTechnicianQuery)
                        SizedBox(height: 28, width: 28, child: ProgressRing()),
                      Expanded(flex: 2, child: SizedBox()),
                    ],
                  ),
                  oldTechnician != technician
                      ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          lang.technicianWillBe(
                            availableTechnicians.firstWhere(
                              (tech) => tech['id'].toString() == technician,
                              orElse: () => {'name': lang.notAssigned},
                            )['name'],
                          ),
                          style: TextStyle(
                            color: Colors.red.lighter,
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                      : (technicianSetter || oldTechnician.isNotEmpty)
                      ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          lang.currentTechnician(
                            availableTechnicians.firstWhere(
                              (tech) => tech['id'].toString() == technician,
                              orElse: () => {'name': lang.notAssigned},
                            )['name'],
                          ),
                          style: TextStyle(
                            color: Colors.green.lighter,
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                      : SizedBox(height: 34),
                  // Device Phase Section
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSectionTitle(lang.device_phases),
                          SizedBox(height: 8),
                          ComboBox<String>(
                            value: phase,

                            items: [
                              ComboBoxItem(
                                value: '0',
                                child: Text(lang.on_hold),
                              ),
                              ComboBoxItem(
                                value: '1',
                                child: Text(lang.diagnostic),
                              ),
                              ComboBoxItem(
                                value: '2',
                                child: Text(lang.confirmation),
                              ),
                              ComboBoxItem(
                                value: '3',
                                child: Text(lang.confirmed),
                              ),
                              ComboBoxItem(
                                value: '4',
                                child: Text(lang.repaired),
                              ),
                              ComboBoxItem(
                                value: '5',
                                child: Text(lang.not_repaired),
                              ),
                              ComboBoxItem(
                                value: '6',
                                child: Text(lang.parts_unavailable),
                              ),

                              ComboBoxItem(
                                value: '7',
                                child: Text(lang.rejected),
                              ),
                            ],
                            onChanged: (value) {
                              setState(() {
                                // phaseSetter = false;
                                phase = value ?? phase;
                              });
                            },
                          ),
                        ],
                      ),
                      const SizedBox(width: 16),
                      SizedBox(
                        height: 33.5,
                        child: Button(
                          onPressed: () async {
                            setState(() => isPhaseQuery = true);
                            await Future.delayed(Duration(milliseconds: 200));
                            try {
                              bool updated = await deviceCrud.updatePhase(
                                machineId: device.machineId,
                                phase: int.parse(phase),
                                user:
                                    'Hamza Halfi', // TODO: Replace with actual user from auth system
                                date: DateTime.now(),
                                time:
                                    DateTime.now()
                                        .toString()
                                        .split(' ')[1]
                                        .split('.')[0], // HH:MM:SS format
                              );
                              if (updated) {
                                // Check if phase requires SMS template
                                if ([
                                  2,
                                  4,
                                  5,
                                  6,
                                  7,
                                ].contains(int.parse(phase))) {
                                  final template = await deviceCrud
                                      .fetchSmsTemplate(
                                        phase: int.parse(phase),
                                        clientName: device.clientName ?? '',
                                        machineType: device.machineType,
                                        machineModel: device.machineModel ?? '',
                                        price: priceController.text,
                                        receiptN: device.billId.toString(),
                                      );
                                  if (template != null) {
                                    smsController.text = template;
                                  }
                                }
                                setState(() {
                                  isPhaseQuery = false;
                                  phaseSetter = true;
                                });

                                // Update the selected device with the new phase
                                final updatedDevice = device.copyWith(
                                  phase: int.parse(phase),
                                );
                                ref
                                    .read(selectedDeviceProvider.notifier)
                                    .state = updatedDevice;

                                // Refresh the device list to reflect the changes
                                ref
                                    .read(
                                      deviceProvider(
                                        'devicesAtWorkshop',
                                      ).notifier,
                                    )
                                    .loadDevices();

                                // Show success message
                                if (context.mounted) {
                                  displayInfoBar(
                                    context,
                                    builder: (context, close) {
                                      return InfoBar(
                                        title: Text(
                                          '${lang.phase} ${lang.update}',
                                        ),
                                        content: Text(
                                          lang.phaseChangedSuccessfully(
                                            phases[int.parse(phase)],
                                          ),
                                        ),
                                        severity: InfoBarSeverity.success,
                                      );
                                    },
                                  );

                                  // Show SMS dialog if template is loaded
                                  if (smsController.text.trim().isNotEmpty) {
                                    Future.delayed(Duration(milliseconds: 500), () {
                                      if (context.mounted) {
                                        showDialog(
                                          context: context,
                                          builder: (
                                            BuildContext dialogContext,
                                          ) {
                                            return ContentDialog(
                                              title: Text(lang.sendSms),
                                              content: Text(
                                                lang.smsTemplateLoadedMessage,
                                              ),
                                              actions: [
                                                Button(
                                                  child: Text(lang.cancel),
                                                  onPressed: () {
                                                    Navigator.of(
                                                      dialogContext,
                                                    ).pop();
                                                  },
                                                ),
                                                SizedBox(width: 8),
                                                FilledButton(
                                                  child: Text(lang.sendSms),
                                                  onPressed: () async {
                                                    Navigator.of(
                                                      dialogContext,
                                                    ).pop();

                                                    // Send SMS directly
                                                    setState(() {
                                                      isSmsQuery = true;
                                                    });

                                                    try {
                                                      final result =
                                                          await deviceCrud.sendSms(
                                                            clientId:
                                                                device.clientId,
                                                            machineId:
                                                                device
                                                                    .machineId,
                                                            destination:
                                                                device
                                                                    .clientPhone ??
                                                                '',
                                                            message:
                                                                smsController
                                                                    .text,
                                                            phase: int.parse(
                                                              phase,
                                                            ),
                                                            user:
                                                                'Hamza Halfi', // TODO: Replace with actual user from auth system
                                                          );

                                                      setState(() {
                                                        isSmsQuery = false;
                                                      });

                                                      if (context.mounted) {
                                                        if (result['success']) {
                                                          displayInfoBar(
                                                            context,
                                                            builder: (
                                                              context,
                                                              close,
                                                            ) {
                                                              return InfoBar(
                                                                title: Text(
                                                                  lang.smsMessage,
                                                                ),
                                                                content: Text(
                                                                  result['message'],
                                                                ),
                                                                severity:
                                                                    InfoBarSeverity
                                                                        .success,
                                                              );
                                                            },
                                                          );
                                                        } else {
                                                          displayInfoBar(
                                                            context,
                                                            builder: (
                                                              context,
                                                              close,
                                                            ) {
                                                              return InfoBar(
                                                                title: Text(
                                                                  lang.smsError,
                                                                ),
                                                                content: Text(
                                                                  result['message'],
                                                                ),
                                                                severity:
                                                                    InfoBarSeverity
                                                                        .error,
                                                              );
                                                            },
                                                          );
                                                        }
                                                      }
                                                    } catch (e) {
                                                      setState(() {
                                                        isSmsQuery = false;
                                                      });

                                                      if (context.mounted) {
                                                        displayInfoBar(
                                                          context,
                                                          builder: (
                                                            context,
                                                            close,
                                                          ) {
                                                            return InfoBar(
                                                              title: Text(
                                                                lang.smsError,
                                                              ),
                                                              content: Text(
                                                                lang.failedToSendSms(
                                                                  e.toString(),
                                                                ),
                                                              ),
                                                              severity:
                                                                  InfoBarSeverity
                                                                      .error,
                                                            );
                                                          },
                                                        );
                                                      }
                                                    }
                                                  },
                                                ),
                                              ],
                                            );
                                          },
                                        );
                                      }
                                    });
                                  }
                                }
                              } else {
                                setState(() => isPhaseQuery = false);
                                // Show error message
                                if (context.mounted) {
                                  displayInfoBar(
                                    context,
                                    builder: (context, close) {
                                      return InfoBar(
                                        title: Text(
                                          '${lang.phase} ${lang.error}',
                                        ),
                                        content: Text(lang.failedToChangePhase),
                                        severity: InfoBarSeverity.error,
                                      );
                                    },
                                  );
                                }
                              }
                            } catch (e) {
                              setState(() => isPhaseQuery = false);
                              // Handle error
                              if (context.mounted) {
                                displayInfoBar(
                                  context,
                                  builder: (context, close) {
                                    return InfoBar(
                                      title: Text(
                                        '${lang.phase} ${lang.error}',
                                      ),
                                      content: Text(
                                        lang.failedToChangePhaseWithError(
                                          e.toString(),
                                        ),
                                      ),
                                      severity: InfoBarSeverity.error,
                                    );
                                  },
                                );
                              }
                            }
                          },
                          child: Text('${lang.change} ${lang.phase}'),
                        ),
                      ),
                      const SizedBox(width: 14),
                      if (isPhaseQuery)
                        SizedBox(height: 33.5, child: ProgressRing()),
                      Spacer(),
                    ],
                  ),
                  // Phase status indicator
                  phaseSetter
                      ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          lang.phaseSuccessfullyChanged(
                            phases[int.parse(phase)],
                          ),
                          style: TextStyle(
                            color: Colors.green.lighter,
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                      : phase != device.phase.toString()
                      ? Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Text(
                          lang.phaseWillChange(
                            phases[device.phase],
                            phases[int.parse(phase)],
                          ),
                          style: TextStyle(
                            color: Colors.red.lighter,
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      )
                      : const SizedBox(height: 34),
                  // SMS TEXT Section
                  Row(
                    children: [
                      Expanded(
                        flex: 5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle(lang.sms_text),
                            const SizedBox(height: 8),
                            TextBox(
                              controller: smsController,
                              minLines: 4,
                              maxLines: 4,
                              placeholder: lang.auto_sms,
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(
                          children: [
                            const SizedBox(height: 24), // Align with text box
                            IconButton(
                              icon:
                                  isSmsQuery
                                      ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: ProgressRing(strokeWidth: 2),
                                      )
                                      : const Icon(CarbonIcons.send, size: 24),
                              onPressed:
                                  isSmsQuery
                                      ? null
                                      : () async {
                                        // Validate SMS message
                                        if (smsController.text.trim().isEmpty) {
                                          if (context.mounted) {
                                            displayInfoBar(
                                              context,
                                              builder: (context, close) {
                                                return InfoBar(
                                                  title: Text(lang.smsMessage),
                                                  content: Text(
                                                    '${lang.enter} SMS message',
                                                  ),
                                                  severity:
                                                      InfoBarSeverity.warning,
                                                );
                                              },
                                            );
                                          }
                                          return;
                                        }

                                        setState(() {
                                          isSmsQuery = true;
                                        });

                                        try {
                                          final result = await deviceCrud.sendSms(
                                            clientId: device.clientId,
                                            machineId: device.machineId,
                                            destination:
                                                device.clientPhone ?? '',
                                            message: smsController.text,
                                            phase: int.parse(phase),
                                            user:
                                                'Hamza Halfi', // TODO: Replace with actual user from auth system
                                          );

                                          setState(() {
                                            isSmsQuery = false;
                                          });

                                          if (context.mounted) {
                                            if (result['success']) {
                                              // Show success message
                                              displayInfoBar(
                                                context,
                                                builder: (context, close) {
                                                  return InfoBar(
                                                    title: Text(
                                                      lang.smsMessage,
                                                    ),
                                                    content: Text(
                                                      result['message'],
                                                    ),
                                                    severity:
                                                        InfoBarSeverity.success,
                                                  );
                                                },
                                              );
                                            } else {
                                              // Show error message
                                              displayInfoBar(
                                                context,
                                                builder: (context, close) {
                                                  return InfoBar(
                                                    title: Text(lang.smsError),
                                                    content: Text(
                                                      result['message'],
                                                    ),
                                                    severity:
                                                        InfoBarSeverity.error,
                                                  );
                                                },
                                              );
                                            }
                                          }
                                        } catch (e) {
                                          setState(() {
                                            isSmsQuery = false;
                                          });

                                          if (context.mounted) {
                                            displayInfoBar(
                                              context,
                                              builder: (context, close) {
                                                return InfoBar(
                                                  title: Text(lang.smsError),
                                                  content: Text(
                                                    lang.failedToSendSms(
                                                      e.toString(),
                                                    ),
                                                  ),
                                                  severity:
                                                      InfoBarSeverity.error,
                                                );
                                              },
                                            );
                                          }
                                        }
                                      },
                            ),
                            const SizedBox(height: 4),
                            Text(lang.send_sms, style: TextStyle(fontSize: 12)),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // REMARKS - SERVICES Section
                  Row(
                    children: [
                      Expanded(
                        flex: 5,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildSectionTitle(
                              '${lang.remarks} - ${lang.services}',
                            ),
                            const SizedBox(height: 8),
                            TextBox(
                              controller: remarksController,
                              minLines: 4,
                              maxLines: 4,
                              placeholder:
                                  '${lang.enter} ${lang.remarks} ${lang.and} ${lang.services}...',
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Column(
                          children: [
                            const SizedBox(height: 24), // Align with text box
                            IconButton(
                              icon:
                                  isRemarksQuery
                                      ? const SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: ProgressRing(strokeWidth: 2),
                                      )
                                      : const Icon(CarbonIcons.save, size: 24),
                              onPressed:
                                  isRemarksQuery
                                      ? null
                                      : () async {
                                        setState(() {
                                          isRemarksQuery = true;
                                        });

                                        try {
                                          final result = await deviceCrud
                                              .saveRemarks(
                                                machineId: device.machineId,
                                                remarks: remarksController.text,
                                              );

                                          setState(() {
                                            isRemarksQuery = false;
                                          });

                                          // Show status message based on result
                                          if (result == 'updated') {
                                            // Show success message for update
                                            if (context.mounted) {
                                              displayInfoBar(
                                                context,
                                                builder: (context, close) {
                                                  return InfoBar(
                                                    title: Text(
                                                      '${lang.remarks} ${lang.update}',
                                                    ),
                                                    content: Text(
                                                      lang.remarksUpdatedSuccessfully,
                                                    ),
                                                    severity:
                                                        InfoBarSeverity.success,
                                                  );
                                                },
                                              );
                                            }
                                          } else if (result == 'saved') {
                                            // Show success message for save
                                            if (context.mounted) {
                                              displayInfoBar(
                                                context,
                                                builder: (context, close) {
                                                  return InfoBar(
                                                    title: Text(
                                                      '${lang.remarks} ${lang.save}',
                                                    ),
                                                    content: Text(
                                                      lang.remarksSavedSuccessfully,
                                                    ),
                                                    severity:
                                                        InfoBarSeverity.success,
                                                  );
                                                },
                                              );
                                            }
                                          } else if (result == 'empty') {
                                            // Show message for empty remarks
                                            if (context.mounted) {
                                              displayInfoBar(
                                                context,
                                                builder: (context, close) {
                                                  return InfoBar(
                                                    title: Text(lang.remarks),
                                                    content: Text(
                                                      '${lang.enter} ${lang.remarks}',
                                                    ),
                                                    severity:
                                                        InfoBarSeverity.warning,
                                                  );
                                                },
                                              );
                                            }
                                          } else {
                                            // Show error message
                                            if (context.mounted) {
                                              displayInfoBar(
                                                context,
                                                builder: (context, close) {
                                                  return InfoBar(
                                                    title: Text(lang.error),
                                                    content: Text(
                                                      lang.failedToSaveRemarks,
                                                    ),
                                                    severity:
                                                        InfoBarSeverity.error,
                                                  );
                                                },
                                              );
                                            }
                                          }
                                        } catch (e) {
                                          setState(() {
                                            isRemarksQuery = false;
                                          });

                                          if (context.mounted) {
                                            displayInfoBar(
                                              context,
                                              builder: (context, close) {
                                                return InfoBar(
                                                  title: Text(lang.error),
                                                  content: Text(
                                                    lang.failedToSaveRemarks,
                                                  ),
                                                  severity:
                                                      InfoBarSeverity.error,
                                                );
                                              },
                                            );
                                          }
                                        }
                                      },
                            ),
                            const SizedBox(height: 4),
                            Text(
                              "${lang.save} ${lang.remarks}",
                              style: TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                FilledButton(
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Text(
                      lang.exit,
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            );
          },
        ),
  );
}

// Helper method to build section titles
Widget _buildSectionTitle(String title) {
  return Text(
    title,
    style: TextStyle(
      fontWeight: FontWeight.w600,
      fontSize: 14,
      color: Colors.blue.lightest,
    ),
  );
}

Future<void> infoBar({
  required BuildContext context,
  required String title,
  required String content,
  required InfoBarSeverity severity,
}) {
  return displayInfoBar(
    context,
    builder: (context, close) {
      return InfoBar(
        title: Text(title),
        content: Text(content),
        action: IconButton(
          icon: const Icon(FluentIcons.clear),
          onPressed: close,
        ),
        severity: severity,
      );
    },
  );
}
