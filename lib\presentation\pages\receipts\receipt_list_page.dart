import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/receipts/widgets/receipt_devices_datagrid.dart';
import 'package:workshop_studio/presentation/pages/receipts/widgets/receipt_list_timeline.dart';
import 'package:workshop_studio/presentation/pages/receipts/widgets/receipts_list_datagrid.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class ReceiptListPage extends StatelessWidget {
  const ReceiptListPage({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return ScaffoldPage.withPadding(
      content: Row(
        spacing: 18,
        children: [
          Flexible(flex: 1, child: ReceiptListTimeline()),
          Flexible(
            flex: 6,
            child: Column(
              spacing: 18.0,
              children: [
                CardHighlight(
                  child: Row(
                    spacing: 18,
                    children: [
                      Expanded(
                        flex: 1,
                        child: TextBox(
                          expands: false,
                          placeholder: lang.search,
                          prefix: Padding(
                            padding: EdgeInsets.all(8),
                            child: Icon(CarbonIcons.search),
                          ),
                        ),
                      ),
                      Spacer(flex: 3),
                      IconButton(
                        icon: Icon(FluentIcons.refresh, size: 18.0),
                        onPressed: () {},
                      ),
                    ],
                  ),
                ),
                CommandBar(
                  overflowBehavior: CommandBarOverflowBehavior.wrap,
                  primaryItems: [
                    CommandBarButton(
                      icon: const Icon(FluentIcons.edit_contact),
                      label: Text(lang.edit_owner),
                      tooltip: lang.edit_owner_tooltip,
                      onPressed: () {
                        // Create something new!
                      },
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.payment_card),
                      label: Text(lang.situation),
                      tooltip: lang.situation_tooltip,
                      onPressed: () {
                        // Create something new!
                      },
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.print),
                      label: Text(lang.print_receipt),
                      onPressed: () {},
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.delete),
                      label: Text(lang.delete_receipt),
                      tooltip: lang.delete_receipt_tooltip,
                      onPressed: () {},
                    ),
                  ],
                ),
                Expanded(flex: 1, child: ReceiptsClientsDataGrid()),
                Divider(),
                CommandBar(
                  overflowBehavior: CommandBarOverflowBehavior.wrap,
                  primaryItems: [
                    CommandBarButton(
                      icon: const Icon(CarbonIcons.queued, size: 22),
                      label: Text(lang.phase),
                      tooltip: lang.phase_tooltip,
                      onPressed: () {
                        // Create something new!
                      },
                    ),
                    CommandBarSeparator(color: Colors.grey[50]),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.add),
                      label: Text(lang.add),
                      tooltip: lang.add_device_tooltip,
                      onPressed: () {
                        // Create something new!
                      },
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.edit),
                      label: Text(lang.edit),
                      tooltip: lang.edit_device_tooltip,
                      onPressed: () {
                        // Create something new!
                      },
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.delete),
                      label: Text(lang.delete),
                      tooltip: lang.remove_device_tooltip,
                      onPressed: () {},
                    ),
                    CommandBarSeparator(color: Colors.grey[50]),
                    CommandBarButton(
                      icon: const Icon(CarbonIcons.delivery, size: 22),
                      label: Text(lang.delivery),
                      tooltip: lang.delivery_tooltip,
                      onPressed: () {},
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.full_history),
                      label: Text(lang.traceability),
                      tooltip: lang.traceability_tooltip,
                      onPressed: () {},
                    ),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.payment_card),
                      label: Text(lang.situation),
                      tooltip: lang.situation_tooltip,
                      onPressed: () {},
                    ),
                  ],
                ),
                Divider(),
                Expanded(
                  flex: 1,
                  child: ListView(
                    shrinkWrap: true,
                    children: [ReceiptDevicesDataGrid()],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
