import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';

class DeleveredDevicesTimeline extends StatelessWidget {
  const DeleveredDevicesTimeline({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    final TextStyle textStyle = TextStyle(fontSize: 13);
    final Widget spaceSize = SizedBox(height: 14);
    DateTime? selected;
    return CardHighlight(
      child: ListView(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 8.0, top: 8, right: 8),
            child: Wrap(
              runAlignment: WrapAlignment.center,
              alignment: WrapAlignment.center,
              runSpacing: 8,
              spacing: 8,
              children: [
                Icon(FluentIcons.date_time2, size: 22, color: Colors.blue),
                Padding(
                  padding: const EdgeInsets.only(top: 3.0),
                  child: Text(lang.timeline),
                ),
              ],
            ),
          ),
          spaceSize,
          Divider(),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: RadioButton(
              checked: false,
              onChanged: (_) {},
              content: Text(lang.all, style: textStyle),
            ),
          ),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: RadioButton(
              checked: true,
              onChanged: (_) {},
              content: Text(lang.current_year, style: textStyle),
            ),
          ),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: RadioButton(
              checked: false,
              onChanged: (_) {},
              content: Text(lang.current_month, style: textStyle),
            ),
          ),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: RadioButton(
              checked: false,
              onChanged: (_) {},
              content: Text(lang.current_week, style: textStyle),
            ),
          ),
          spaceSize,
          Divider(),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: RadioButton(
              checked: false,
              onChanged: (_) {},
              content: Text(lang.timeline_picker, style: textStyle),
            ),
          ),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: DatePicker(
              header: lang.from,
              selected: selected,
              fieldFlex: const [2, 3, 2], // Same order as fieldOrder
              onChanged: (_) {}, // (time) => setState(() => selected = time),
            ),
          ),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: DatePicker(
              header: lang.to,
              selected: selected,
              fieldFlex: const [2, 3, 2], // Same order as fieldOrder
              onChanged: (_) {}, // (time) => setState(() => selected = time),
            ),
          ),
          spaceSize,
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Button(child: Text(lang.select), onPressed: () {}),
          ),
          spaceSize,
          Divider(),
          spaceSize,
          ListTile(
            leading: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                color: Colors.yellow.darkest,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            title: Text(lang.confirmation, style: textStyle),
            onPressed: () {},
          ),
          ListTile(
            leading: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                color: Colors.blue.darkest,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            title: Text(lang.confirmed, style: textStyle),
            onPressed: () {},
          ),
          ListTile(
            leading: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                color: Colors.errorPrimaryColor,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            title: Text(lang.rejected, style: textStyle),
            onPressed: () {},
          ),
          ListTile(
            leading: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                color: Colors.green.darkest,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            title: Text(lang.repaired, style: textStyle),
            onPressed: () {},
          ),
          ListTile(
            leading: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                color: Colors.purple.darkest,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            title: Text(lang.not_repaired, style: textStyle),
            onPressed: () {},
          ),
          ListTile(
            leading: Container(
              width: 15,
              height: 15,
              decoration: BoxDecoration(
                color: Colors.teal.darkest,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            title: Text(lang.parts_unavailable, style: textStyle),
            onPressed: () {},
          ),
        ],
      ),
    );
  }
}
