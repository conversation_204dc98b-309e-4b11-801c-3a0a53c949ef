import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/providers/clients/payment/client_payment_crud_provider.dart';

void showClientPaymentDialog(BuildContext context, WidgetRef ref) {
  // final lang = AppLocalizations.of(context);
  final dialogWidth = MediaQuery.of(context).size.width / 3;
  final dialogHeight = MediaQuery.of(context).size.height / 1.93;

  final debtsController = TextEditingController();
  final netPayableController = TextEditingController();

  List<String> paymentChoices = [
    "Cash",
    "Cheques",
    "Bank card",
    "Bank transfer",
  ];
  String paymentChoice = paymentChoices[0];

  final noteController = TextEditingController();

  late bool isValiding = false;
  late bool inProgress = false;
  final selectedClient = ref.read(selectedClientProvider.notifier).state;
  final paymentProvider = ref.read(clientPaymentCrudProvider);

  showDialog(
    context: context,
    builder:
        (context) => StatefulBuilder(
          builder: (context, setState) {
            Widget buildField(String label, Widget field) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 6),
                child: Row(
                  children: [
                    Expanded(flex: 1, child: Text(label)),
                    Expanded(flex: 2, child: field),
                  ],
                ),
              );
            }

            return ContentDialog(
              constraints: BoxConstraints.expand(
                width: dialogWidth,
                height: dialogHeight,
              ),
              title: const Text(
                'Client Payment',
                style: TextStyle(fontSize: 18),
              ),
              content: ScaffoldPage.scrollable(
                children: [
                  CardHighlight(
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        buildField(
                          'Date',
                          Text("${DateTime.now()}".substring(0, 11)),
                        ),
                        SizedBox(height: 10),
                        buildField(
                          "Client name",
                          Text(selectedClient?.cName ?? 'Select a client'),
                        ),
                        SizedBox(height: 10),
                        buildField(
                          'Payment choice',
                          ComboBox<String>(
                            isExpanded: true,
                            value: paymentChoice,
                            items: List.generate(
                              paymentChoices.length,
                              (index) => ComboBoxItem(
                                value: paymentChoices[index],
                                child: Text(paymentChoices[index]),
                              ),
                            ),
                            onChanged:
                                (value) =>
                                    setState(() => paymentChoice = value!),
                          ),
                        ),
                        buildField(
                          'Debts',
                          TextBox(
                            style: TextStyle(
                              letterSpacing: 1.2,
                              fontWeight: FontWeight.w500,
                            ),
                            keyboardType: TextInputType.phone,
                            minLines: 1,
                            maxLines: 1,
                            expands: false,
                            padding: const EdgeInsets.all(10),
                            controller: debtsController,
                            placeholder: '0.00',
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                          ),
                        ),
                        buildField(
                          'Net payable',
                          TextBox(
                            style: TextStyle(
                              letterSpacing: 1.2,
                              fontWeight: FontWeight.w500,
                            ),
                            keyboardType: TextInputType.phone,
                            minLines: 1,
                            maxLines: 1,
                            expands: false,
                            padding: const EdgeInsets.all(10),
                            controller: netPayableController,
                            placeholder: 'Phone Number',
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                          ),
                        ),
                        buildField(
                          'Transaction note',
                          SizedBox(
                            height: 80,
                            child: TextBox(
                              maxLines: null,
                              padding: EdgeInsets.all(10),
                              controller: noteController,
                              placeholder: 'Enter note !',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              actions: [
                Button(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(isValiding ? 'Exit' : 'Cancel'),
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
                FilledButton(
                  child: Padding(
                    padding: EdgeInsets.all(inProgress ? 2.0 : 8.0),
                    child:
                        inProgress
                            ? SizedBox(
                              height: 28,
                              width: 28,
                              child: ProgressRing(),
                            )
                            : Text(
                              isValiding ? 'Print payment bill' : 'Validate',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                  ),
                  onPressed: () async {
                    if (selectedClient == null) return;
                    setState(() => inProgress = true);
                    await paymentProvider.createPayment(
                      clientId: selectedClient.id,
                      amount: double.parse(netPayableController.text),
                      paymentMethod: paymentChoices.indexOf(paymentChoice),
                      note: noteController.text,
                    );
                    setState(() => inProgress = false);
                    setState(() => isValiding = true);
                  },
                ),
              ],
            );
          },
        ),
  );
}
