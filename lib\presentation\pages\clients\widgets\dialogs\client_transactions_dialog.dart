import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/client/clients_table_model.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datagrid/transactions_tva__history_datagrid.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/dialogs/client_situation_dialog.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datagrid/transaction_history_devices_datagrid.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datagrid/transactions_history_datagrid.dart';
import 'package:workshop_studio/providers/clients/transactions/client_transactions_notifier.dart';

void showClientTransactionsDialog(
  BuildContext context,
  ClientModel selectedClient,
) {
  final dialogWidth = MediaQuery.of(context).size.width / 1;
  final dialogHeight = MediaQuery.of(context).size.height / 1;
  final lang = AppLocalizations.of(context);

  showDialog(
    context: context,
    builder:
        (context) => Consumer(
          builder: (context, ref, _) {
            return StatefulBuilder(
              builder: (context, setState) {
                return ContentDialog(
                  constraints: BoxConstraints.expand(
                    width: dialogWidth,
                    height: dialogHeight,
                  ),
                  title: Text(
                    "Transactions history",
                    style: TextStyle(fontSize: 18),
                  ),
                  content: ScaffoldPage.withPadding(
                    content: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: EdgeInsets.only(bottom: 18),
                          child: Text(
                            selectedClient.cName,
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color:
                                  FluentTheme.of(context).accentColor.lightest,
                            ),
                          ),
                        ),
                        CommandBarCard(
                          child: CommandBar(
                            primaryItems: [
                              CommandBarButton(
                                icon: const Icon(FluentIcons.print),
                                label: Text("Print"),
                                tooltip: 'lang.add_client_tooltip',
                                onPressed: () {},
                              ),
                              CommandBarSeparator(color: Colors.grey[50]),
                              CommandBarButton(
                                icon: const Icon(FluentIcons.edit),
                                label: Text(lang.edit),
                                tooltip: 'lang.edit_client_tooltip',
                                onPressed:
                                    () => showClientSituationDialog(
                                      context,
                                      selectedClient.cTva
                                          ? ref
                                              .read(
                                                selectedTransactionTvaProvider
                                                    .notifier,
                                              )
                                              .state
                                          : ref
                                              .read(
                                                selectedTransactionProvider
                                                    .notifier,
                                              )
                                              .state,
                                    ),
                              ),
                              CommandBarButton(
                                icon: const Icon(FluentIcons.delete),
                                label: Text(lang.delete),
                                tooltip: 'lang.delete_client_tooltip',
                                onPressed: () async {
                                  await unselectedInfoBar(context);
                                  // if (selectedClient == null) {
                                  // return;
                                  // }
                                },
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 18),
                        Expanded(
                          flex: 1,
                          child: Column(
                            spacing: 10,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("Transactions list"),
                              Expanded(
                                child:
                                    selectedClient.cTva
                                        ? TransactionsTvaHistoryDatagrid()
                                        : TransactionsHistoryDatagrid(),
                              ),
                            ],
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            spacing: 10,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("Transactions devices"),
                              Expanded(
                                child: TransactionHistoryDevicesDatagrid(),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  actions: [
                    FilledButton(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: const Text('Exit'),
                      ),
                      onPressed: () {
                        Navigator.pop(context);
                      },
                    ),
                  ],
                );
              },
            );
          },
        ),
  );
}

Future<void> unselectedInfoBar(BuildContext context) {
  return displayInfoBar(
    context,
    builder: (context, close) {
      return InfoBar(
        title: const Text('You can not do that :/'),
        content: const Text('Please select a transaction first :/'),
        action: IconButton(
          icon: const Icon(FluentIcons.clear),
          onPressed: close,
        ),
        severity: InfoBarSeverity.warning,
      );
    },
  );
}
