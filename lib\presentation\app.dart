import 'package:carbon_icons/carbon_icons.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/cashflow/cash_flow_page.dart';
import 'package:workshop_studio/presentation/pages/clients/clients_page.dart';
import 'package:workshop_studio/presentation/pages/dashboard/dashboard_page.dart';
import 'package:workshop_studio/presentation/pages/devices/devices_page.dart';
import 'package:workshop_studio/presentation/pages/stock/stock_page.dart';
import 'package:workshop_studio/presentation/pages/receipts/receipt_list_page.dart';
import 'package:workshop_studio/presentation/pages/reception/reception_page.dart';
import 'package:workshop_studio/presentation/pages/settings/settings_page.dart';
import 'package:workshop_studio/presentation/pages/technicians/technicians_page.dart';
import 'package:workshop_studio/providers/language_provider.dart';
import 'package:workshop_studio/providers/theme_provider.dart';
import '../providers/navigation_provider.dart';
import 'package:window_manager/window_manager.dart';

class MainScreen extends ConsumerStatefulWidget {
  const MainScreen({super.key});
  @override
  ConsumerState<MainScreen> createState() => _MainScreen();
}

class _MainScreen extends ConsumerState<MainScreen> with WindowListener {
  @override
  void initState() {
    super.initState();
    windowManager.addListener(this);
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(navigationProvider);
    final currentLangue = ref.watch(appLanguageProvider);
    final TextStyle paneTitleStyle = FluentTheme.of(
      context,
    ).typography.body!.copyWith(fontWeight: FontWeight.w400, fontSize: 13);
    final double paneIconSize = 24;

    return NavigationView(
      appBar: NavigationAppBar(
        title: DragToMoveArea(
          child: Align(
            alignment: AlignmentDirectional.centerStart,
            child: Text.rich(
              style: FluentTheme.of(context).typography.bodyLarge!.copyWith(
                fontFamily: "Audiowide",
                fontWeight: FontWeight.bold,
              ),
              TextSpan(
                children: [
                  TextSpan(text: 'WORKSHOP '),
                  TextSpan(
                    text: 'Studio',
                    style: TextStyle(color: Colors.blue),
                  ),
                ],
              ),
            ),
          ),
        ),
        leading: IconButton(
          icon: Icon(CarbonIcons.help, size: paneIconSize),
          onPressed: () {},
        ),
        actions: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            IconButton(
              iconButtonMode: IconButtonMode.large,
              onPressed: () {
                ref.read(themeProvider.notifier).toggleTheme();
              },
              icon: Icon(FluentIcons.contrast, size: 18),
            ),
            const SizedBox(width: 8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Tooltip(
                message: AppLocalizations.of(context).language,
                child: ComboBox<String>(
                  value: currentLangue.languageCode,
                  items: [
                    ComboBoxItem(value: 'en', child: Text('En')),
                    ComboBoxItem(value: 'fr', child: Text('Fr')),
                    ComboBoxItem(value: 'ar', child: Text('Ar')),
                  ],
                  onChanged: (code) {
                    ref
                        .read(appLanguageProvider.notifier)
                        .changeLanguage(Locale(code!));
                  },
                ),
              ),
            ),
            WindowButtons(),
          ],
        ),
      ),
      pane: NavigationPane(
        size: NavigationPaneSize(openWidth: 200),
        displayMode: PaneDisplayMode.open,
        selected: currentIndex,
        onChanged:
            (index) => ref.read(navigationProvider.notifier).state = index,
        header: Container(
          margin: EdgeInsets.symmetric(vertical: 20),
          child: Row(
            children: [
              Container(
                width: 35,
                height: 35,
                decoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    'H',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 10),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Hamza Halfi',
                    style: FluentTheme.of(context).typography.bodyLarge
                        ?.copyWith(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'Admin',
                    style: FluentTheme.of(context).typography.caption?.copyWith(
                      color:
                          FluentTheme.of(
                            context,
                          ).resources.textFillColorSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        items: [
          PaneItem(
            icon: Icon(CarbonIcons.chart_average, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).dashboard,
              style: paneTitleStyle,
            ),
            body: DashboardPage(),
          ),
          PaneItem(
            icon: Icon(CarbonIcons.help_desk, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).reception,
              style: paneTitleStyle,
            ),
            body: ReceptionPage(),
          ),
          PaneItem(
            icon: Icon(CarbonIcons.events, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).clients,
              style: paneTitleStyle,
            ),
            body: ClientsPage(),
          ),
          PaneItem(
            icon: Icon(CarbonIcons.devices, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).devices,
              style: paneTitleStyle,
            ),
            body: DevicesPage(),
          ),
          PaneItem(
            icon: Icon(CarbonIcons.list, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).receiptList,
              style: paneTitleStyle,
            ),
            body: ReceiptListPage(),
          ),
          PaneItem(
            icon: Icon(CarbonIcons.dashboard_reference, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).cashFlow,
              style: paneTitleStyle,
            ),
            body: CashFlowPage(),
          ),
          // PaneItem(
          //   icon: Icon(CarbonIcons.wallet, size: paneIconSize),
          //   title: Text(
          //     AppLocalizations.of(context).expenses,
          //     style: paneTitleStyle,
          //   ),
          //   body: ExpensesPage(),
          // ),
          PaneItem(
            icon: Icon(FluentIcons.teamwork, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).technicians,
              style: paneTitleStyle,
            ),
            body: TechniciansPage(),
          ),
          PaneItem(
            icon: Icon(FluentIcons.product_list, size: paneIconSize),
            title: Text("Stock", style: paneTitleStyle),
            body: StockPage(),
          ),
        ],
        footerItems: [
          PaneItem(
            icon: Icon(CarbonIcons.settings, size: paneIconSize),
            title: Text(
              AppLocalizations.of(context).settings,
              style: paneTitleStyle,
            ),
            body: SettingsPage(),
          ),
        ],
      ),
    );
  }

  @override
  void onWindowClose() async {
    bool isPreventClose = await windowManager.isPreventClose();
    if (isPreventClose && mounted) {
      showDialog(
        context: context,
        builder: (_) {
          return ContentDialog(
            title: Text(AppLocalizations.of(context).exitApp),
            content: Text(AppLocalizations.of(context).exitAppMsg),
            actions: [
              FilledButton(
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Text(AppLocalizations.of(context).yes),
                ),
                onPressed: () {
                  Navigator.pop(context);
                  windowManager.destroy();
                },
              ),
              Button(
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Text(AppLocalizations.of(context).no),
                ),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
            ],
          );
        },
      );
    }
  }
}

class WindowButtons extends StatelessWidget with WindowListener {
  const WindowButtons({super.key});

  @override
  Widget build(BuildContext context) {
    final FluentThemeData theme = FluentTheme.of(context);

    return SizedBox(
      width: 138,
      height: 50,
      child: WindowCaption(
        brightness: theme.brightness,
        backgroundColor: Colors.transparent,
      ),
    );
  }
}
