import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/models/device/device_model.dart';
import 'package:workshop_studio/providers/devices/at_workshop/at_workshop_notifier.dart';

class DevicesAtworkshopDataSource extends DataGridSource {
  final BuildContext context;
  // final DeviceModel? selectedDevice;
  final WidgetRef ref;
  DevicesAtworkshopDataSource(
    this.context, {
    required List<DeviceModel> devices,
    // this.selectedDevice,
    required this.ref,
  }) {
    _dataGridRows =
        devices.map<DataGridRow>((d) {
          return DataGridRow(
            cells: [
              DataGridCell<int>(columnName: 'id', value: d.machineId),
              DataGridCell<String>(columnName: 'client', value: d.clientName),
              DataGridCell<String>(columnName: 'type', value: d.machineType),
              DataGridCell<String>(columnName: 'brand', value: d.machineBrand),
              DataGridCell<String>(columnName: 'serei', value: d.machineSerie),
              DataGridCell<String>(columnName: 'model', value: d.machineModel),
              DataGridCell<String>(
                columnName: 'serial_number',
                value: d.serialNumber,
              ),
              DataGridCell<String>(columnName: 'issue', value: d.problem),
              DataGridCell<int>(columnName: 'deadline', value: d.deadline),
              DataGridCell<int>(columnName: 'phase', value: d.phase),
              DataGridCell<String>(columnName: 'user', value: d.technician),
              DataGridCell<String>(columnName: 'date', value: d.date),
            ],
          );
        }).toList();
    notifyListeners();
  }

  List<DataGridRow> _dataGridRows = [];

  @override
  List<DataGridRow> get rows => _dataGridRows;

  Color textColor = Colors.black;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    final lang = AppLocalizations.of(context);
    final selectedDevice = ref.watch(selectedDeviceProvider);
    final List<String> phases = [
      lang.on_hold,
      lang.diagnostic,
      lang.confirmation,
      lang.confirmed,
      lang.repaired,
      lang.not_repaired,
      lang.parts_unavailable,
      lang.rejected,
    ];
    final List<Color> phaseBadgeColors = [
      Colors.grey, // on_hold
      Colors.blue, // diagnostic
      Colors.orange, // confirmation
      Color(0xff2980b9), // confirmed
      Colors.green, // repaired
      Colors.red, // not_repaired
      Color.fromARGB(255, 141, 28, 79), // parts_unavailable
      Color(0xffc0392b), // rejected
    ];
    Color getRowBackgroundColor() {
      final int id =
          row.getCells().firstWhere((cell) => cell.columnName == 'id').value;

      if (selectedDevice != null && selectedDevice.machineId == id) {
        return Colors
            .blue
            .lightest; // Highlight selected row with lightest blue
      }
      // Return default background color (no phase-based row coloring)
      return Colors.transparent;
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map((cell) {
            // Use standard text color for all cells
            final bool isDark =
                FluentTheme.of(context).brightness == Brightness.dark;
            final Color cellTextColor = isDark ? Colors.white : Colors.black;

            return Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(8.0),
              child:
                  cell.columnName == 'phase'
                      ? _buildPhaseBadge(cell.value, phases, phaseBadgeColors)
                      : Text(
                        cell.value.toString().toUpperCase(),
                        textAlign: TextAlign.center,
                        style: TextStyle(color: cellTextColor),
                      ),
            );
          }).toList(),
    );
  }

  /// Build a colored phase badge
  Widget _buildPhaseBadge(
    int phaseValue,
    List<String> phases,
    List<Color> colors,
  ) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: colors[phaseValue],
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Text(
        phases[phaseValue],
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12.0,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
