class Coupon {
  final int? id;
  final String? workshopName;
  final String? address;
  final String? tlfFax;
  final String? mobil;
  final String? email;
  final String? website;
  final String conditions;

  Coupon({
    this.id,
    this.workshopName,
    this.address,
    this.tlfFax,
    this.mobil,
    this.email,
    this.website,
    required this.conditions,
  });

  factory Coupon.fromJson(Map<String, dynamic> json) {
    return Coupon(
      id: json['id'] as int?,
      workshopName: json['workshop_name'] as String?,
      address: json['address'] as String?,
      tlfFax: json['tlf_fax'] as String?,
      mobil: json['mobil'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
      conditions: json['conditions'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'workshop_name': workshopName,
      'address': address,
      'tlf_fax': tlfFax,
      'mobil': mobil,
      'email': email,
      'website': website,
      'conditions': conditions,
    };
  }
}
