import 'package:fluent_ui/fluent_ui.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/dialogs/client_contacts_dialog.dart';
import 'package:workshop_studio/providers/clients/client/client_crud_provider.dart';
import 'package:workshop_studio/providers/clients/client/clients_notifier.dart';
import 'package:workshop_studio/models/client/client_details_model.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/client_search_box.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/datagrid/clients_data_grid.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/clients_timeline.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/dialogs/client_delete_dialog.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/dialogs/client_details_dialog.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/dialogs/client_payment_dialog.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/dialogs/client_transactions_dialog.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/pages/clients/widgets/dialogs/client_add_edit_dialog.dart';

class ClientsPage extends ConsumerWidget {
  const ClientsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lang = AppLocalizations.of(context);
    return ScaffoldPage.withPadding(
      content: Row(
        children: [
          ClientsTimeline(),
          SizedBox(width: 20),
          Flexible(
            flex: 6,
            child: Column(
              spacing: 10,
              children: [
                CardHighlight(
                  child: Row(
                    spacing: 18,
                    children: [
                      Expanded(flex: 1, child: ClientSearchBox()),
                      Spacer(flex: 2),
                      IconButton(
                        icon: Icon(FluentIcons.refresh, size: 18.0),
                        onPressed: () {
                          ref.read(clientProvider.notifier).reset();
                        },
                      ),
                    ],
                  ),
                ),
                CommandBarCard(
                  child: CommandBar(
                    overflowBehavior: CommandBarOverflowBehavior.wrap,
                    primaryItems: [
                      CommandBarButton(
                        icon: const Icon(FluentIcons.receipt_processing),
                        label: Text(lang.new_reception),
                        tooltip: lang.new_reception_tooltip,
                        onPressed: () {
                          // Create something new!
                        },
                      ),
                      CommandBarSeparator(color: Colors.grey[50]),
                      CommandBarButton(
                        icon: const Icon(FluentIcons.add),
                        label: Text(lang.add),
                        tooltip: lang.add_client_tooltip,
                        onPressed:
                            () => showClientAddEditDialog(
                              context,
                              ref,
                              null,
                              false,
                            ),
                      ),
                      CommandBarButton(
                        icon: const Icon(FluentIcons.edit),
                        label: Text(lang.edit),
                        tooltip: lang.edit_client_tooltip,
                        onPressed: () async {
                          final selectedClient = ref.read(
                            selectedClientProvider,
                          );
                          if (selectedClient == null) {
                            await unselectedClientInfoBar(context);
                            return;
                          }
                          showClientAddEditDialog(
                            context,
                            ref,
                            selectedClient,
                            true,
                          );
                        },
                      ),
                      CommandBarButton(
                        icon: const Icon(FluentIcons.delete),
                        label: Text(lang.delete),
                        tooltip: lang.delete_client_tooltip,
                        onPressed: () async {
                          final selectedClient = ref.read(
                            selectedClientProvider,
                          );
                          if (selectedClient == null) {
                            await unselectedClientInfoBar(context);
                            return;
                          }

                          final confirmed = await showDeleteConfirmationDialog(
                            context,
                          );
                          if (confirmed) {
                            await ref
                                .read(clientCrudProvider)
                                .deleteClient(selectedClient.id);
                            ref.read(clientProvider.notifier).reset();
                            await displayInfoBar(
                              // ignore: use_build_context_synchronously
                              context,
                              builder: (context, close) {
                                return InfoBar(
                                  title: const Text(
                                    'You are delete a client :/',
                                  ),
                                  content: Text(
                                    '${selectedClient.cName} has been deleted successfully',
                                  ),
                                  action: IconButton(
                                    icon: const Icon(FluentIcons.clear),
                                    onPressed: close,
                                  ),
                                  severity: InfoBarSeverity.success,
                                );
                              },
                            );
                          }
                        },
                      ),
                      CommandBarSeparator(color: Colors.grey[50]),
                      CommandBarButton(
                        icon: const Icon(FluentIcons.contact_info),
                        label: Text('Contacts'),
                        tooltip: 'lang.delete_client_tooltip',
                        onPressed: () async {
                          final selectedClient = ref.read(
                            selectedClientProvider,
                          );
                          if (selectedClient == null) {
                            await unselectedClientInfoBar(context);
                            return;
                          }
                          showMoreContactsDialog(context, ref, selectedClient);
                        },
                      ),
                      CommandBarButton(
                        icon: const Icon(FluentIcons.info),
                        label: Text('Details'),
                        tooltip: 'lang.delete_client_tooltip',
                        onPressed: () {
                          final selectedClient = ref.read(
                            selectedClientProvider,
                          );
                          if (selectedClient != null) {
                            showClientDetailsDialog(
                              context,
                              ClientDetailsModel(
                                city: selectedClient.cCity,
                                address: selectedClient.cAddress,
                                email: selectedClient.cEmail,
                                rc: selectedClient.cRc,
                                nif: selectedClient.cNif,
                                ai: selectedClient.cAi,
                                nis: selectedClient.cNis,
                                tva: selectedClient.cTva,
                              ),
                            );
                          }
                        },
                      ),
                      CommandBarSeparator(color: Colors.grey[50]),
                      CommandBarButton(
                        icon: const Icon(FluentIcons.payment_card),
                        label: Text(lang.payment),
                        tooltip: lang.client_payment_tooltip,
                        onPressed: () async {
                          final selectedClient = ref.read(
                            selectedClientProvider,
                          );
                          if (selectedClient == null) {
                            await unselectedClientInfoBar(context);
                            return;
                          }
                          showClientPaymentDialog(context, ref);
                        },
                      ),
                      CommandBarButton(
                        icon: const Icon(FluentIcons.align_left),
                        label: Text(lang.transactions),
                        tooltip: lang.transactions_tooltip,
                        onPressed: () async {
                          final selectedClient = ref.read(
                            selectedClientProvider,
                          );
                          if (selectedClient == null) {
                            await unselectedClientInfoBar(context);
                            return;
                          }
                          showClientTransactionsDialog(context, selectedClient);
                        },
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(child: ClientsDataGrid()),
                      // Expanded(flex: 1, child: ReceptionDataGrid()),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> unselectedClientInfoBar(BuildContext context) {
    return displayInfoBar(
      context,
      builder: (context, close) {
        return InfoBar(
          title: const Text('You can not do that :/'),
          content: const Text('Please select a client first'),
          action: IconButton(
            icon: const Icon(FluentIcons.clear),
            onPressed: close,
          ),
          severity: InfoBarSeverity.warning,
        );
      },
    );
  }
}
