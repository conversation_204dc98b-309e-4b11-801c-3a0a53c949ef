class UserPermissionModel {
  final int id;
  final int userId;
  final Map<String, bool> permissions;

  UserPermissionModel({
    required this.id,
    required this.userId,
    required this.permissions,
  });

  factory UserPermissionModel.fromJson(Map<String, dynamic> json) {
    return UserPermissionModel(
      id: json['id'],
      userId: json['user_id'],
      permissions: _permissionsFromJson(json),
    );
  }

  static Map<String, bool> _permissionsFromJson(Map<String, dynamic> json) {
    final permissions = <String, bool>{};
    json.forEach((key, value) {
      if (key != 'id' && key != 'user_id') {
        permissions[key] = value == 1;
      }
    });
    return permissions;
  }

  Map<String, dynamic> toJson() => {
    'user_id': userId,
    ...permissions.map((k, v) => MapEntry(k, v ? 1 : 0)),
  };
}
