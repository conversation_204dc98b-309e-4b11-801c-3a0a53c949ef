import 'package:carbon_icons/carbon_icons.dart';
import 'package:fluent_ui/fluent_ui.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/widgets/card_highlight.dart';

class DataInputs extends StatelessWidget {
  const DataInputs({super.key});

  @override
  Widget build(BuildContext context) {
    final lang = AppLocalizations.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Flexible(
          flex: 2,
          child: Column(
            children: [
              CardHighlight(
                child: Column(
                  children: [
                    CommandBarCard(
                      child: CommandBar(
                        overflowBehavior: CommandBarOverflowBehavior.wrap,
                        primaryItems: [
                          CommandBarButton(
                            icon: const Icon(FluentIcons.add_friend),
                            label: Text(lang.new_add),
                            tooltip: lang.add_client_tooltip,
                            onPressed: () {
                              // Create something new!
                            },
                          ),
                          const CommandBarSeparator(),
                          CommandBarButton(
                            icon: const Icon(FluentIcons.edit),
                            label: Text(lang.edit),
                            tooltip: lang.edit_client_tooltip,
                            onPressed: () {},
                          ),
                          const CommandBarSeparator(),
                          CommandBarButton(
                            icon: const Icon(FluentIcons.list),
                            label: Text(lang.select),
                            tooltip: lang.select_client_tooltip,
                            onPressed: () {},
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(flex: 1, child: Text(lang.name)),
                                    Expanded(
                                      flex: 3,
                                      child: TextBox(
                                        enabled: false,
                                        prefix: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Icon(CarbonIcons.user),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 18),
                                Row(
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: Text(lang.phone_n),
                                    ),
                                    Expanded(
                                      flex: 3,
                                      child: TextBox(
                                        enabled: false,
                                        prefix: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Icon(CarbonIcons.phone),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(height: 18),
                                Row(
                                  children: [
                                    Expanded(flex: 1, child: Text(lang.type)),
                                    Expanded(
                                      flex: 3,
                                      child: TextBox(
                                        enabled: false,
                                        prefix: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Icon(CarbonIcons.list),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 18),
              CardHighlight(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.device_type)),
                          Expanded(
                            flex: 3,
                            child: TextBox(
                              prefix: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(CarbonIcons.edge_device),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.brand)),
                          Expanded(
                            flex: 3,
                            child: TextBox(
                              prefix: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(CarbonIcons.data_2),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.serie)),
                          Expanded(
                            flex: 3,
                            child: TextBox(
                              prefix: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(CarbonIcons.data_2),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.model)),
                          Expanded(
                            flex: 3,
                            child: TextBox(
                              prefix: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(CarbonIcons.data_2),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: SizedBox()),
                          Expanded(
                            flex: 3,
                            child: Row(
                              spacing: 8,
                              children: [
                                Expanded(
                                  flex: 2,
                                  child: TextBox(
                                    placeholder: "PREFIX",
                                    prefix: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Icon(CarbonIcons.data_2),
                                    ),
                                  ),
                                ),
                                Expanded(
                                  flex: 1,
                                  child: NumberBox(
                                    textAlign: TextAlign.center,
                                    clearButton: false,
                                    value: 0,
                                    onChanged: (_) {},
                                    mode: SpinButtonPlacementMode.none,
                                  ),
                                ),
                                Tooltip(
                                  message: lang.generate,
                                  child: IconButton(
                                    icon: Icon(CarbonIcons.barcode, size: 24),
                                    onPressed: () {},
                                  ),
                                ),
                                Tooltip(
                                  message: lang.print_barcode_tooltip,
                                  child: IconButton(
                                    icon: Icon(FluentIcons.print, size: 24),
                                    onPressed: () {},
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.serial_n)),
                          Expanded(
                            flex: 3,
                            child: TextBox(
                              prefix: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(CarbonIcons.data_2),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: 50),
        Flexible(
          flex: 2,
          child: Column(
            children: [
              CardHighlight(
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.issue)),
                          Expanded(
                            flex: 3,
                            child: TextBox(
                              prefix: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Icon(CarbonIcons.warning_alt),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.deadline)),
                          Expanded(
                            flex: 3,
                            child: Row(
                              spacing: 18,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: NumberBox(
                                    textAlign: TextAlign.center,
                                    clearButton: false,
                                    value: 0,
                                    onChanged: (_) {},
                                    mode: SpinButtonPlacementMode.none,
                                  ),
                                ),
                                Expanded(flex: 2, child: Text(lang.days)),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.estimated_price)),
                          Expanded(
                            flex: 3,
                            child: Row(
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: NumberBox(
                                    textAlign: TextAlign.center,
                                    clearButton: true,
                                    value: 0,
                                    onChanged: (_) {},
                                    mode: SpinButtonPlacementMode.none,
                                  ),
                                ),
                                SizedBox(width: 18),
                                Expanded(flex: 2, child: SizedBox()),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        children: [
                          Expanded(flex: 1, child: Text(lang.remarks)),
                          Expanded(
                            flex: 3,
                            child: SizedBox(
                              height: 200.0,
                              child: TextBox(maxLines: null),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 18),
                      Row(
                        spacing: 18,
                        children: [
                          Checkbox(
                            checked: false,
                            onChanged: (v) {},
                            content: Text(lang.warranty),
                          ),
                          Checkbox(
                            checked: false,
                            onChanged: (v) {},
                            content: Text(lang.emergency),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 18),
              CardHighlight(
                child: Wrap(
                  spacing: 5,
                  runSpacing: 18,
                  children: [
                    FilledButton(
                      style: ButtonStyle(
                        backgroundColor: WidgetStatePropertyAll(
                          FluentTheme.of(context).accentColor.lightest,
                        ),
                        padding: WidgetStatePropertyAll(EdgeInsets.all(10)),
                      ),
                      child: Text(
                        lang.print_receipt,
                        style: TextStyle(
                          color: FluentTheme.of(context).activeColor,
                        ),
                      ),
                      onPressed: () {},
                    ),
                    SizedBox(width: 18),
                    Button(
                      style: ButtonStyle(
                        padding: WidgetStatePropertyAll(EdgeInsets.all(10)),
                      ),
                      child: Text(lang.cancel_reception),
                      onPressed: () {},
                    ),
                    SizedBox(width: double.tryParse('10E+1')),
                    Checkbox(
                      checked: true,
                      onChanged: (onChanged) {},
                      content: Text(lang.close_after_printing),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 18),
              CardHighlight(
                child: CommandBar(
                  overflowBehavior: CommandBarOverflowBehavior.wrap,
                  primaryItems: [
                    CommandBarButton(
                      icon: const Icon(FluentIcons.add),
                      label: Text(lang.add),
                      tooltip: lang.add_device_tooltip,
                      onPressed: () {
                        // Create something new!
                      },
                    ),
                    const CommandBarSeparator(),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.edit),
                      label: Text(lang.edit),
                      tooltip: lang.edit_device_tooltip,
                      onPressed: () {},
                    ),
                    const CommandBarSeparator(),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.cancel),
                      label: Text(lang.cancel),
                      tooltip: lang.cancel_device_tooltip,
                      onPressed: () {},
                    ),
                    const CommandBarSeparator(),
                    CommandBarButton(
                      icon: const Icon(FluentIcons.remove),
                      label: Text(lang.delete),
                      tooltip: lang.remove_device_tooltip,
                      onPressed: () {},
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Flexible(flex: 1, child: SizedBox()),
      ],
    );
  }
}
