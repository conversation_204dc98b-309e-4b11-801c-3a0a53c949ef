import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/services/database_service.dart';

final mysqlServiceProvider = Provider<MySQLService>((ref) {
  final service = MySQLService();
  ref.onDispose(() => service.close());
  return service;
});

final mysqlConnectionProvider = FutureProvider<MySQLService>((ref) async {
  final service = MySQLService();
  ref.onDispose(() => service.close());
  return service;
});
