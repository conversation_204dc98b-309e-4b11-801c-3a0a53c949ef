import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/models/client/client_sold_model.dart';

class SituationDataSource extends DataGridSource {
  final BuildContext context;
  final List<ClientSoldModel> transactions;
  late final List<DataGridRow> _rows;
  List<String> paymentType = ["Cash", "Cheques", "Bank card", "Bank transfer"];

  SituationDataSource(this.context, {required this.transactions}) {
    _rows =
        transactions
            .map<DataGridRow>(
              (t) => DataGridRow(
                cells: [
                  DataGridCell<String>(
                    columnName: 'ID',
                    value: t.sId.toString(),
                  ),
                  DataGridCell<double>(
                    columnName: 'CurrentBalance',
                    value: t.currentBalance,
                  ),
                  DataGridCell<double>(columnName: 'Payment', value: t.payment),
                  DataGridCell<String>(
                    columnName: 'Type',
                    value: paymentType[t.paymentType],
                  ),
                  DataGridCell<double>(
                    columnName: 'OldBalance',
                    value: t.oldBalance,
                  ),
                  DataGridCell<double>(
                    columnName: 'Total',
                    value: t.oldBalance + t.payment,
                  ),
                  DataGridCell<double>(
                    columnName: 'NewBalance',
                    value: t.newBalance,
                  ),
                  DataGridCell<String>(columnName: 'Date', value: t.date),
                  DataGridCell<String>(
                    columnName: 'Time',
                    value: t.time.substring(0, 5),
                  ),
                  DataGridCell<String>(
                    columnName: 'Warranty',
                    value: t.dateWarranty ?? 'N/A',
                  ),
                  DataGridCell<String>(columnName: 'Note', value: t.note ?? ''),
                ],
              ),
            )
            .toList();
  }

  @override
  List<DataGridRow> get rows => _rows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    return DataGridRowAdapter(
      cells:
          row.getCells().map((cell) {
            return Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(8.0),
              child: Text(
                textAlign: TextAlign.center,
                cell.value.toString().toUpperCase(),
              ),
            );
          }).toList(),
    );
  }
}
