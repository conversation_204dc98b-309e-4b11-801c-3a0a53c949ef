// Unified SMS Template Model

/// Unified model for all SMS template types
class SmsTemplateModel {
  final int id;
  final String message;
  final SmsTemplateType type;

  SmsTemplateModel({
    required this.id,
    required this.message,
    required this.type,
  });

  /// Factory constructor for repaired SMS
  factory SmsTemplateModel.repaired({
    required int id,
    required String message,
  }) {
    return SmsTemplateModel(
      id: id,
      message: message,
      type: SmsTemplateType.repaired,
    );
  }

  /// Factory constructor for not repaired SMS
  factory SmsTemplateModel.notRepaired({
    required int id,
    required String message,
  }) {
    return SmsTemplateModel(
      id: id,
      message: message,
      type: SmsTemplateType.notRepaired,
    );
  }

  /// Factory constructor for to confirm SMS
  factory SmsTemplateModel.toConfirm({
    required int id,
    required String message,
  }) {
    return SmsTemplateModel(
      id: id,
      message: message,
      type: SmsTemplateType.toConfirm,
    );
  }

  /// Factory constructor for missing pieces SMS
  factory SmsTemplateModel.missingPieces({
    required int id,
    required String message,
  }) {
    return SmsTemplateModel(
      id: id,
      message: message,
      type: SmsTemplateType.missingPieces,
    );
  }

  /// Factory constructor for rejected SMS
  factory SmsTemplateModel.rejected({
    required int id,
    required String message,
  }) {
    return SmsTemplateModel(
      id: id,
      message: message,
      type: SmsTemplateType.rejected,
    );
  }

  /// Create from JSON based on template type
  factory SmsTemplateModel.fromJson(
    Map<String, dynamic> json,
    SmsTemplateType type,
  ) {
    return SmsTemplateModel(
      id: int.tryParse(json['id']?.toString() ?? '') ?? 0,
      message: json[type.columnName] ?? '',
      type: type,
    );
  }

  /// Convert to JSON for database operations
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      type.columnName: message,
    };
  }

  /// Create a copy with updated values
  SmsTemplateModel copyWith({
    int? id,
    String? message,
    SmsTemplateType? type,
  }) {
    return SmsTemplateModel(
      id: id ?? this.id,
      message: message ?? this.message,
      type: type ?? this.type,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SmsTemplateModel &&
        other.id == id &&
        other.message == message &&
        other.type == type;
  }

  @override
  int get hashCode => Object.hash(id, message, type);

  @override
  String toString() {
    return 'SmsTemplateModel(id: $id, message: $message, type: ${type.displayName})';
  }
}

/// Enum for SMS template types
enum SmsTemplateType {
  repaired,
  notRepaired,
  toConfirm,
  missingPieces,
  rejected,
}

/// Extension for SmsTemplateType utilities
extension SmsTemplateTypeExtension on SmsTemplateType {
  /// Get the database table name for this template type
  String get tableName {
    switch (this) {
      case SmsTemplateType.repaired:
        return 'repaired_sms';
      case SmsTemplateType.notRepaired:
        return 'not_repaired_sms';
      case SmsTemplateType.toConfirm:
        return 'to_confirm_sms';
      case SmsTemplateType.missingPieces:
        return 'missing_pieces_sms';
      case SmsTemplateType.rejected:
        return 'rejected_sms';
    }
  }

  /// Get the database column name for this template type
  String get columnName {
    switch (this) {
      case SmsTemplateType.repaired:
        return 'repaired';
      case SmsTemplateType.notRepaired:
        return 'not_repaired';
      case SmsTemplateType.toConfirm:
        return 'to_confirm';
      case SmsTemplateType.missingPieces:
        return 'missing_pieces';
      case SmsTemplateType.rejected:
        return 'rejected';
    }
  }

  /// Get the display name for UI
  String get displayName {
    switch (this) {
      case SmsTemplateType.repaired:
        return 'Repaired';
      case SmsTemplateType.notRepaired:
        return 'Not Repaired';
      case SmsTemplateType.toConfirm:
        return 'To Confirm';
      case SmsTemplateType.missingPieces:
        return 'Missing Pieces';
      case SmsTemplateType.rejected:
        return 'Rejected';
    }
  }

  /// Get icon for UI (you can customize these)
  String get icon {
    switch (this) {
      case SmsTemplateType.repaired:
        return '✅';
      case SmsTemplateType.notRepaired:
        return '❌';
      case SmsTemplateType.toConfirm:
        return '❓';
      case SmsTemplateType.missingPieces:
        return '🔧';
      case SmsTemplateType.rejected:
        return '🚫';
    }
  }
}

/// Helper class for SMS template operations
class SmsTemplateHelper {
  /// Get all available template types
  static List<SmsTemplateType> get allTypes => SmsTemplateType.values;

  /// Create a template model from database row
  static SmsTemplateModel fromDatabaseRow(
    Map<String, dynamic> row,
    SmsTemplateType type,
  ) {
    return SmsTemplateModel.fromJson(row, type);
  }

  /// Get template type from table name
  static SmsTemplateType? getTypeFromTableName(String tableName) {
    for (final type in SmsTemplateType.values) {
      if (type.tableName == tableName) {
        return type;
      }
    }
    return null;
  }
}
