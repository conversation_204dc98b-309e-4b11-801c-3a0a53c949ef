import 'package:fluent_ui/fluent_ui.dart';
import 'package:window_manager/window_manager.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workshop_studio/l10n/gen_l10n/app_localizations.dart';
import 'package:workshop_studio/presentation/themes.dart';
import 'package:workshop_studio/providers/language_provider.dart';
import 'package:workshop_studio/providers/theme_provider.dart';
import 'package:workshop_studio/routes/app_router.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await windowManager.ensureInitialized();
  await WindowManager.instance.ensureInitialized();
  windowManager.waitUntilReadyToShow().then((_) async {
    await windowManager.setTitleBarStyle(
      TitleBarStyle.hidden,
      windowButtonVisibility: false,
    );
    // await windowManager.setMinimumSize(const Size(1200, 700));
    await windowManager.maximize();
    await windowManager.center();
    await windowManager.show();
    await windowManager.setPreventClose(true);
    await windowManager.setSkipTaskbar(false);
  });
  runApp(const ProviderScope(child: WorkshopStudio()));
}

class WorkshopStudio extends ConsumerWidget {
  const WorkshopStudio({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appLocale = ref.watch(appLanguageProvider);
    final themeModeProvider = ref.watch(themeProvider);
    return FluentApp.router(
      title: 'Workshop Studio',
      debugShowCheckedModeBanner: false,
      theme: lightTheme,
      darkTheme: darkTheme,
      themeMode: themeModeProvider,
      routerConfig: appRouter,
      locale: appLocale,
      localizationsDelegates: AppLocalizations.localizationsDelegates,
      supportedLocales: AppLocalizations.supportedLocales,
    );
  }
}
