// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get language => 'اللغة';

  @override
  String get dashboard => 'لوحة القيادة';

  @override
  String get reception => 'الاستقبال';

  @override
  String get clients => 'العملاء';

  @override
  String get devices => 'الأجهزة';

  @override
  String get device => 'الجهاز';

  @override
  String get receiptList => 'قائمة الإيصالات';

  @override
  String get cashFlow => 'التدفق النقدي';

  @override
  String get expenses => 'المصروفات';

  @override
  String get products => 'المنتجات';

  @override
  String get technicians => 'الفنيين';

  @override
  String get settings => 'الإعدادات';

  @override
  String get exitApp => 'تأكيد الإغلاق';

  @override
  String get exitAppMsg => 'هل أنت متأكد أنك تريد إغلاق هذه النافذة؟';

  @override
  String get yes => 'نعم';

  @override
  String get no => 'لا';

  @override
  String get new_add => 'جديد';

  @override
  String get add => 'أضف';

  @override
  String get edit => 'تعديل';

  @override
  String get cancel => 'الغاء';

  @override
  String get delete => 'حذف';

  @override
  String get select => 'اختيار';

  @override
  String get name => 'الاسم';

  @override
  String get type => 'نوع';

  @override
  String get phone_n => 'رقم الهاتف';

  @override
  String get device_type => 'نوع الجهاز';

  @override
  String get brand => 'ماركة';

  @override
  String get serie => 'سلسلة';

  @override
  String get model => 'نموذج';

  @override
  String get serial_n => 'الرقم التسلسلي';

  @override
  String get issue => 'الاعطال';

  @override
  String get deadline => 'التسليم بعد';

  @override
  String get days => 'يوم';

  @override
  String get estimated_price => 'السعر التقديري';

  @override
  String get remarks => 'ملاحظات';

  @override
  String get warranty => 'ضمان';

  @override
  String get emergency => 'طارئ';

  @override
  String get print_receipt => 'طباعة الإيصال';

  @override
  String get cancel_reception => 'إلغاء الاستقبال';

  @override
  String get close_after_printing => 'إغلاق بعد الطباعة';

  @override
  String get id => 'رقم التعريف';

  @override
  String get generate => 'مولد الباركود';

  @override
  String get print_barcode_tooltip => 'طباعة الباركود';

  @override
  String get add_client_tooltip => 'إضافة عميل جديد';

  @override
  String get edit_client_tooltip => 'تعديل معلومات العميل المحددة';

  @override
  String get select_client_tooltip => 'اختر من قائمة العملاء الموجودة';

  @override
  String get add_device_tooltip => 'إضافة جهاز جديد إلى القائمة';

  @override
  String get edit_device_tooltip => 'تعديل معلومات الجهاز المحدد';

  @override
  String get cancel_device_tooltip => 'إلغاء التحرير';

  @override
  String get remove_device_tooltip => 'إزالة الجهاز من القائمة';

  @override
  String get timeline => 'الخط الزمني';

  @override
  String get all => 'الكل';

  @override
  String get current_year => 'السنة الحالية';

  @override
  String get current_month => 'الشهر الحالي';

  @override
  String get current_week => 'الاسبوع الحالي';

  @override
  String get timeline_picker => 'اختيار الجدول الزمني';

  @override
  String get search => 'البحث';

  @override
  String get no_filter => 'بدون فلترة';

  @override
  String get dealer => 'تاجر';

  @override
  String get particular => 'خاص';

  @override
  String get company => 'شركة';

  @override
  String get new_reception => 'استقبال جديد';

  @override
  String get payment => 'الدفع';

  @override
  String get transactions => 'سجل المعاملات';

  @override
  String get user => 'المستخدم';

  @override
  String get debts => 'الديون';

  @override
  String get time => 'الوقت';

  @override
  String get date => 'التاريخ';

  @override
  String get from => 'من';

  @override
  String get to => 'الى';

  @override
  String get new_reception_tooltip => 'إنشاء استقبال جديد للعميل المحدد';

  @override
  String get delete_client_tooltip => 'حذف العميل المحدد';

  @override
  String get client_payment_tooltip => 'إضافة الدفع للعميل المحدد';

  @override
  String get transactions_tooltip => 'إدارة سجل معاملات العميل المحدد';

  @override
  String get phase => 'مرحلة';

  @override
  String get phase_tooltip => 'إدارة مرحلة الجهاز';

  @override
  String get technician => 'الفني';

  @override
  String get technician_tooltip => 'الفني الذي عمل على هذا الجهاز';

  @override
  String get print_device => 'طباعة';

  @override
  String get print_device_tooltip => 'طباعة إيصال مالك الجهاز';

  @override
  String get situation => 'الحالة';

  @override
  String get situation_tooltip => 'إدارة حالة مالك الجهاز';

  @override
  String get delivery => 'تسليم الجهاز';

  @override
  String get delivery_tooltip => 'تغيير حالة الجهاز إلى مرحلة التسليم';

  @override
  String get traceability => 'التتبع';

  @override
  String get traceability_tooltip => 'إمكانية تتبع الجهاز المحدد';

  @override
  String get client => 'العميل';

  @override
  String get on_hold => 'قيد الانتظار';

  @override
  String get diagnostic => 'التشخيص';

  @override
  String get confirmation => 'تأكيد';

  @override
  String get confirmed => 'مؤكد';

  @override
  String get repaired => 'تم إصلاحه';

  @override
  String get not_repaired => 'لم يتم اصلاحه';

  @override
  String get rejected => 'مرفوض';

  @override
  String get parts_unavailable => 'قطع غيار غير متوفرة';

  @override
  String get devices_at_workshop => 'الأجهزة في ورشة العمل';

  @override
  String get delivered_devices => 'الأجهزة المُسلَّمة';

  @override
  String get cancel_delivery => 'إلغاء التسليم';

  @override
  String get cancel_delivery_tooltip => 'إلغاء تسليم الجهاز';

  @override
  String get edit_owner => 'تعديل المالك';

  @override
  String get edit_owner_tooltip => 'تغيير مالك الإيصال (العميل)';

  @override
  String get delete_receipt => 'حذف الإيصال';

  @override
  String get delete_receipt_tooltip => 'حذف الإيصال نهائيًا';

  @override
  String get receipt_n => 'رقم الإيصال';

  @override
  String get can_not => 'لا يمكنك فعل ذلك :/';

  @override
  String get select_device => 'يرجى اختيار جهاز أولاً';

  @override
  String get non => 'non';

  @override
  String get device_managment => 'إدارة مراحل الجهاز';

  @override
  String get device_price => 'سعر الجهاز';

  @override
  String get est_to_final => 'من السعر المقدر إلى السعر النهائي';

  @override
  String get price => 'السعر';

  @override
  String get set => 'تعيين';

  @override
  String get update => 'تحديث';

  @override
  String get loading => 'جاري التحميل';

  @override
  String get found => 'تم العثور عليه';

  @override
  String get device_phases => 'مراحل الجهاز';

  @override
  String get change => 'تغيير';

  @override
  String get sms_text => 'رسالة نصية قصيرة';

  @override
  String get auto_sms => 'رسالة نصية قصيرة تلقائية...';

  @override
  String get send_sms => 'إرسال رسالة نصية قصيرة';

  @override
  String get services => 'خدمات';

  @override
  String get enter => 'أدخل';

  @override
  String get and => 'و';

  @override
  String get save => 'حفظ';

  @override
  String get exit => 'خروج';

  @override
  String get final_price => 'السعر النهائي';

  @override
  String get priceUpdatedSuccessfully => 'تم تحديث السعر بنجاح';

  @override
  String get technicianAssignedSuccessfully => 'تم تعيين الفني بنجاح';

  @override
  String phaseChangedSuccessfully(String phase) {
    return 'تم تغيير المرحلة إلى \"$phase\" بنجاح';
  }

  @override
  String get remarksUpdatedSuccessfully => 'تم تحديث الملاحظات بنجاح';

  @override
  String get remarksSavedSuccessfully => 'تم حفظ الملاحظات بنجاح';

  @override
  String get failedToUpdatePrice => 'فشل في تحديث السعر';

  @override
  String failedToUpdatePriceWithError(String error) {
    return 'فشل في تحديث السعر: $error';
  }

  @override
  String get failedToAssignTechnician => 'فشل في تعيين الفني';

  @override
  String failedToAssignTechnicianWithError(String error) {
    return 'فشل في تعيين الفني: $error';
  }

  @override
  String get failedToChangePhase => 'فشل في تغيير المرحلة';

  @override
  String failedToChangePhaseWithError(String error) {
    return 'فشل في تغيير المرحلة: $error';
  }

  @override
  String get failedToSaveRemarks => 'فشل في حفظ الملاحظات';

  @override
  String failedToSendSms(String error) {
    return 'فشل في إرسال الرسالة النصية: $error';
  }

  @override
  String get sendSms => 'إرسال رسالة نصية';

  @override
  String get smsTemplateLoadedMessage =>
      'تم تحميل نموذج رسالة نصية لهذه المرحلة. هل تريد إرسال إشعار الرسالة النصية للعميل الآن؟';

  @override
  String get smsMessage => 'رسالة نصية';

  @override
  String get smsError => 'خطأ في الرسالة النصية';

  @override
  String get error => 'خطأ';

  @override
  String technicianWillBe(String technician) {
    return 'سيكون الفني: \"$technician\"';
  }

  @override
  String currentTechnician(String technician) {
    return 'الفني الحالي: $technician';
  }

  @override
  String phaseSuccessfullyChanged(String phase) {
    return 'تم تغيير المرحلة بنجاح إلى \"$phase\"';
  }

  @override
  String phaseWillChange(String oldPhase, String newPhase) {
    return 'ستتغير المرحلة من \"$oldPhase\" إلى \"$newPhase\"';
  }

  @override
  String get ok => 'موافق';

  @override
  String get unknown => 'غير معروف';

  @override
  String get notAssigned => 'غير مُعيَّن';

  @override
  String get smsSentSuccessfully => 'تم إرسال الرسالة النصية بنجاح';

  @override
  String get failedToSendSmsViaFrontline =>
      'فشل في إرسال الرسالة النصية عبر Frontline';

  @override
  String smsServiceTimeout(String message) {
    return 'انتهت مهلة خدمة الرسائل النصية: $message';
  }

  @override
  String get frontlineSmsNotConfigured => 'Frontline SMS غير مُكوَّن';

  @override
  String get connectionTimedOut => 'انتهت مهلة الاتصال';

  @override
  String get valid => 'صالح';

  @override
  String get deviceUpdatedSuccessfully => 'تم تحديث الجهاز بنجاح';

  @override
  String get failedToUpdateDevice => 'فشل في تحديث الجهاز';
}
