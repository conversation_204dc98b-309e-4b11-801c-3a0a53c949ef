import 'package:fluent_ui/fluent_ui.dart';
import 'package:syncfusion_flutter_datagrid/datagrid.dart';
import 'package:workshop_studio/models/client/client_soldtva_model.dart';

class TransactionsTvaDataSource extends DataGridSource {
  final BuildContext context;
  final List<ClientSoldTvaModel> transactionsTva;
  late final List<DataGridRow> _rows;
  List<String> paymentType = ["Cash", "Cheques", "Bank card", "Bank transfer"];

  TransactionsTvaDataSource(this.context, {required this.transactionsTva}) {
    _rows =
        transactionsTva
            .map<DataGridRow>(
              (t) => DataGridRow(
                cells: [
                  DataGridCell<String>(
                    columnName: 'ID',
                    value: t.id.toString(),
                  ),
                  DataGridCell<double>(columnName: 'THT', value: t.tht),
                  DataGridCell<int>(columnName: 'TVA', value: t.tvav),
                  DataGridCell<double>(columnName: 'TOTAL TVA', value: t.ttva),
                  DataGridCell<double>(columnName: 'NET PAYABLE', value: t.ttc),
                  DataGridCell<String>(
                    columnName: 'Type',
                    value: paymentType[t.paymentType],
                  ),
                  DataGridCell<String>(columnName: 'Date', value: t.date),
                  DataGridCell<String>(
                    columnName: 'Time',
                    value: t.time.substring(0, 5),
                  ),
                  DataGridCell<String>(
                    columnName: 'Warranty',
                    value: t.dateWarranty ?? 'N/A',
                  ),
                  DataGridCell<String>(columnName: 'Note', value: t.note ?? ''),
                ],
              ),
            )
            .toList();
  }

  @override
  List<DataGridRow> get rows => _rows;

  @override
  DataGridRowAdapter buildRow(DataGridRow row) {
    Color getRowBackgroundColor() {
      final int index = effectiveRows.indexOf(row);
      if (index % 2 != 0) {
        return Colors.blue.lightest.withAlpha(50); // Alternate row color
      }
      return Colors.transparent; // Default row color
    }

    return DataGridRowAdapter(
      color: getRowBackgroundColor(),
      cells:
          row.getCells().map((cell) {
            return Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.all(8.0),
              child: Text(
                textAlign: TextAlign.center,
                cell.value.toString().toUpperCase(),
              ),
            );
          }).toList(),
    );
  }
}
